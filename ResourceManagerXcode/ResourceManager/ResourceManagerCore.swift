import Foundation
import Combine
import Cocoa

// MARK: - Debug Configuration
// Set to true to enable detailed debug logging
private let DEBUG_LOGGING = false

// MARK: - Authentication Delegate
class AuthenticationDelegate: NSObject, URLSessionDelegate {
    func urlSession(_ session: URLSession, didReceive challenge: URLAuthenticationChallenge, completionHandler: @escaping (URLSession.AuthChallengeDisposition, URLCredential?) -> Void) {

        // Level 3: Debug logging - authentication challenge details
        if DEBUG_LOGGING { print("🔐 Received authentication challenge: \(challenge.protectionSpace.authenticationMethod)") }

        // Handle Kerberos/Negotiate authentication
        if challenge.protectionSpace.authenticationMethod == NSURLAuthenticationMethodNegotiate {
            // Level 3: Debug logging - Kerberos authentication
            if DEBUG_LOGGING { print("🎫 Handling Kerberos/Negotiate authentication") }

            // For Kerberos, we should use performDefaultHandling to let the system handle it
            // The system will automatically use available Kerberos tickets
            completionHandler(.performDefaultHandling, nil)
        } else if challenge.protectionSpace.authenticationMethod == NSURLAuthenticationMethodServerTrust {
            // Handle server trust (SSL certificate validation)
            // Level 3: Debug logging - server trust authentication
            if DEBUG_LOGGING { print("🔒 Handling server trust authentication") }
            completionHandler(.performDefaultHandling, nil)
        } else {
            // Level 3: Debug logging - unsupported authentication method
            if DEBUG_LOGGING { print("❌ Unsupported authentication method: \(challenge.protectionSpace.authenticationMethod)") }
            completionHandler(.performDefaultHandling, nil)
        }
    }
}

// MARK: - Resource Creation Result
struct ResourceCreationResult {
    let resourceId: Int
    let resourceLink: String?
    let title: String?
    let priority: Int?
    let location: String?
}

// MARK: - Print Result
struct PrintResult {
    let filename: String
    let fullPath: String
}

// MARK: - Application Service Response
struct ServiceResponse<T> {
    let isSuccess: Bool
    let data: T?
    let message: String?
    let errorCode: String?

    static func success(_ data: T) -> ServiceResponse<T> {
        return ServiceResponse(isSuccess: true, data: data, message: nil, errorCode: nil)
    }

    static func failure(_ message: String, errorCode: String? = nil) -> ServiceResponse<T> {
        return ServiceResponse(isSuccess: false, data: nil, message: message, errorCode: errorCode)
    }
}

// MARK: - Application Service
@MainActor
class ApplicationService: ObservableObject {
    @Published var isLoading = false

    let outputSubject = PassthroughSubject<String, Never>()
    var outputPublisher: AnyPublisher<String, Never> {
        outputSubject.eraseToAnyPublisher()
    }

    private var resourceManager: ResourceManagerCore

    init() {
        self.resourceManager = ResourceManagerCore()
        self.resourceManager.applicationService = self
        // Level 3: Debug logging - service initialization
        if DEBUG_LOGGING { print("🔧 ApplicationService initialized") }
    }

    // MARK: - Public Methods

    func createSingleResource(
        title: String?,
        resdefId: String?,
        priority: String?,
        quantity: String?,
        location: String?,
        categoryId: String?
    ) async -> ServiceResponse<[ResourceCreationResult]> {

        if DEBUG_LOGGING {
            if DEBUG_LOGGING { outputSubject.send("🎯 ApplicationService.createSingleResource called\n") }
            if DEBUG_LOGGING { outputSubject.send("📥 Raw input parameters:\n") }
            if DEBUG_LOGGING { outputSubject.send("   title: '\(title ?? "nil")'\n") }
            if DEBUG_LOGGING { outputSubject.send("   resdefId: '\(resdefId ?? "nil")'\n") }
            if DEBUG_LOGGING { outputSubject.send("   priority: '\(priority ?? "nil")'\n") }
            if DEBUG_LOGGING { outputSubject.send("   quantity: '\(quantity ?? "nil")'\n") }
            if DEBUG_LOGGING { outputSubject.send("   location: '\(location ?? "nil")'\n") }
            if DEBUG_LOGGING { outputSubject.send("   categoryId: '\(categoryId ?? "nil")'\n") }
        }

        isLoading = true
        defer {
            if DEBUG_LOGGING { outputSubject.send("🔄 ApplicationService.createSingleResource defer block - setting isLoading = false\n") }
            isLoading = false
        }

        if DEBUG_LOGGING { outputSubject.send("🔄 ApplicationService: Starting do-catch block...\n") }

        do {
            if DEBUG_LOGGING {
                if DEBUG_LOGGING { outputSubject.send("🔄 ApplicationService: Inside do block, about to call resourceManager.createResource...\n") }
                if DEBUG_LOGGING { outputSubject.send("📥 ApplicationService processed parameters:\n") }
                if DEBUG_LOGGING { outputSubject.send("   title: \(title ?? "nil")\n") }
                if DEBUG_LOGGING { outputSubject.send("   resdefId: \(resdefId ?? "nil")\n") }
                if DEBUG_LOGGING { outputSubject.send("   priority: \(Int(priority ?? "5") ?? 5)\n") }
                if DEBUG_LOGGING { outputSubject.send("   quantity: \(Int(quantity ?? "1") ?? 1)\n") }
                if DEBUG_LOGGING { outputSubject.send("   location: \(location ?? "Aruba")\n") }
                if DEBUG_LOGGING { outputSubject.send("   categoryId: \(categoryId ?? "nil")\n") }
            }

            let results = try await resourceManager.createResource(
                title: title,
                resdefId: resdefId,
                priority: Int(priority ?? "5") ?? 5,
                quantity: Int(quantity ?? "1") ?? 1,
                location: location ?? ResourceManagerConfig.shared.specificLocation,
                categoryId: categoryId
            )

            if DEBUG_LOGGING {
                if DEBUG_LOGGING { outputSubject.send("✅ ApplicationService: resourceManager.createResource returned successfully!\n") }
                if DEBUG_LOGGING { outputSubject.send("📊 Created \(results.count) resources\n") }
                for (index, result) in results.enumerated() {
                    if DEBUG_LOGGING { outputSubject.send("   Resource \(index + 1): ID=\(result.resourceId), link=\(result.resourceLink ?? "nil")\n") }
                }
                if let firstResult = results.first {
                    if DEBUG_LOGGING { outputSubject.send("📋 First resource data:\n") }
                    if DEBUG_LOGGING { outputSubject.send("   Title: \(firstResult.title ?? "N/A")\n") }
                    if DEBUG_LOGGING { outputSubject.send("   Priority: \(firstResult.priority ?? 0) (from Keywords API)\n") }
                    if DEBUG_LOGGING { outputSubject.send("   Location: \(firstResult.location ?? "N/A")\n") }
                }
            }

            if DEBUG_LOGGING { outputSubject.send("✅ Successfully created \(results.count) resource(s)\n") }
            for result in results {
                if DEBUG_LOGGING { outputSubject.send("🔗 Resource \(result.resourceId): \(result.resourceLink ?? "rdar://res/\(result.resourceId)")\n") }
            }

            return .success(results)
        } catch {
            if DEBUG_LOGGING {
                if DEBUG_LOGGING { outputSubject.send("❌ ApplicationService: Caught error in createSingleResource\n") }
                if DEBUG_LOGGING { outputSubject.send("❌ Error type: \(type(of: error))\n") }
                if DEBUG_LOGGING { outputSubject.send("❌ Error description: \(error.localizedDescription)\n") }
                if DEBUG_LOGGING { outputSubject.send("❌ Error: \(error)\n") }
            }

            let errorMessage = "❌ Failed to create resource: \(error.localizedDescription)"
            if DEBUG_LOGGING { outputSubject.send("\(errorMessage)\n") }

            // Check if this is a categoryId error and return appropriate error code
            if let resourceError = error as? ResourceManagerError {
                if DEBUG_LOGGING { outputSubject.send("🔍 ApplicationService: Error is ResourceManagerError\n") }
                if case .categoryIdError(let message) = resourceError {
                    if DEBUG_LOGGING { outputSubject.send("🔍 ApplicationService: Error is categoryIdError with message: \(message)\n") }
                    return ServiceResponse<[ResourceCreationResult]>(
                        isSuccess: false,
                        data: nil,
                        message: message,
                        errorCode: "CATEGORY_ID_ERROR"
                    )
                } else {
                    if DEBUG_LOGGING { outputSubject.send("🔍 ApplicationService: Error is ResourceManagerError but not categoryIdError\n") }
                }
            } else {
                if DEBUG_LOGGING { outputSubject.send("🔍 ApplicationService: Error is NOT ResourceManagerError\n") }
            }

            if DEBUG_LOGGING { outputSubject.send("🔄 ApplicationService: Returning failure response\n") }
            return .failure(errorMessage)
        }
    }

    func createResourcesFromCSV(_ fileURL: URL) async -> ServiceResponse<[ResourceCreationResult]> {
        isLoading = true
        defer { isLoading = false }

        do {
            let results = try await resourceManager.createResourcesFromCSV(fileURL: fileURL)

            if DEBUG_LOGGING { outputSubject.send("✅ Created \(results.count) resources from CSV\n") }
            for result in results {
                if DEBUG_LOGGING { outputSubject.send("   Resource ID: \(result.resourceId)\n") }
            }

            return .success(results)
        } catch {
            let errorMessage = "❌ Failed to create resources from CSV: \(error.localizedDescription)"
            if DEBUG_LOGGING { outputSubject.send("\(errorMessage)\n") }
            return .failure(errorMessage)
        }
    }

    func updateResources(_ fileURL: URL) async -> ServiceResponse<[ResourceCreationResult]> {
        isLoading = true
        defer { isLoading = false }

        if DEBUG_LOGGING { outputSubject.send("🔄 Starting resource update process...\n") }
        if DEBUG_LOGGING { outputSubject.send("📁 File: \(fileURL.lastPathComponent)\n") }
        if DEBUG_LOGGING { outputSubject.send("📍 Path: \(fileURL.path)\n") }

        // Check if file exists
        guard FileManager.default.fileExists(atPath: fileURL.path) else {
            let errorMessage = "❌ File not found at path: \(fileURL.path)"
            if DEBUG_LOGGING { outputSubject.send("\(errorMessage)\n") }
            return .failure(errorMessage)
        }

        if DEBUG_LOGGING { outputSubject.send("✅ File exists, starting validation...\n") }

        do {
            let results = try await resourceManager.updateResourcesFromCSV(fileURL: fileURL)

            if !results.isEmpty {
                if DEBUG_LOGGING { outputSubject.send("✅ Successfully updated \(results.count) resource(s)\n") }

                // Generate combined resource link with & separator (correct format)
                let resourceIds = results.map { String($0.resourceId) }
                let combinedLink = "rdar://res/\(resourceIds.joined(separator: "&"))"
                if DEBUG_LOGGING { outputSubject.send("🔗 Combined Resource Link: \(combinedLink)\n") }

                for result in results {
                    if DEBUG_LOGGING { outputSubject.send("   • Resource \(result.resourceId): \(result.resourceLink ?? "rdar://res/\(result.resourceId)")\n") }
                }
            } else {
                if DEBUG_LOGGING { outputSubject.send("⚠️ No resources were updated\n") }
            }

            return .success(results)
        } catch {
            let errorMessage = "❌ Failed to update resources: \(error.localizedDescription)"
            if DEBUG_LOGGING { outputSubject.send("\(errorMessage)\n") }
            return .failure(errorMessage)
        }
    }

    func printResources() async -> ServiceResponse<String> {
        isLoading = true
        defer { isLoading = false }

        do {
            let outputPath = try await resourceManager.printResources()

            if DEBUG_LOGGING { outputSubject.send("✅ Print file generated: \(outputPath)\n") }
            return .success(outputPath)
        } catch {
            let errorMessage = "❌ Failed to generate print file: \(error.localizedDescription)"
            if DEBUG_LOGGING { outputSubject.send("\(errorMessage)\n") }
            return .failure(errorMessage)
        }
    }

    func updateSingleResource(
        resourceId: Int,
        title: String?,
        priority: Int?,
        location: String
    ) async -> ServiceResponse<[ResourceCreationResult]> {

        if DEBUG_LOGGING {
            if DEBUG_LOGGING { outputSubject.send("🎯 ApplicationService.updateSingleResource called\n") }
            if DEBUG_LOGGING { outputSubject.send("📥 Parameters:\n") }
            if DEBUG_LOGGING { outputSubject.send("   resourceId: \(resourceId)\n") }
            if DEBUG_LOGGING { outputSubject.send("   title: '\(title ?? "nil")'\n") }
            if DEBUG_LOGGING { outputSubject.send("   priority: \(priority?.description ?? "nil (empty)")\n") }
            if DEBUG_LOGGING { outputSubject.send("   location: '\(location)'\n") }
        }

        isLoading = true
        defer {
            // Level 3: Debug logging - defer block execution
            if DEBUG_LOGGING { print("🔄 ApplicationService.updateSingleResource defer block - setting isLoading = false") }
            isLoading = false
        }

        do {
            let result = try await resourceManager.updateSingleResource(
                resourceId: resourceId,
                title: title,
                priority: priority,
                location: location
            )

            if DEBUG_LOGGING {
                if DEBUG_LOGGING { outputSubject.send("✅ ApplicationService: resourceManager.updateSingleResource returned successfully!\n") }
                if DEBUG_LOGGING { outputSubject.send("📊 Result: resourceId=\(result.resourceId), link=\(result.resourceLink ?? "nil")\n") }
            }

            if DEBUG_LOGGING { outputSubject.send("✅ Resource updated successfully: \(result.resourceId)\n") }
            if let link = result.resourceLink {
                if DEBUG_LOGGING { outputSubject.send("🔗 Resource Link: \(link)\n") }
            }

            return .success([result])
        } catch {
            if DEBUG_LOGGING {
                if DEBUG_LOGGING { outputSubject.send("❌ ApplicationService: Caught error in updateSingleResource\n") }
                if DEBUG_LOGGING { outputSubject.send("❌ Error type: \(type(of: error))\n") }
                if DEBUG_LOGGING { outputSubject.send("❌ Error description: \(error.localizedDescription)\n") }
                if DEBUG_LOGGING { outputSubject.send("❌ Error: \(error)\n") }
            }

            let errorMessage = "❌ Failed to update resource: \(error.localizedDescription)"
            if DEBUG_LOGGING { outputSubject.send("\(errorMessage)\n") }

            return .failure(errorMessage)
        }
    }

    func downloadUpdateTemplate(to url: URL) async -> ServiceResponse<Void> {
        do {
            let templateContent = createUpdateTemplateContent()
            try templateContent.write(to: url, atomically: true, encoding: .utf8)

            if DEBUG_LOGGING { outputSubject.send("✅ Template downloaded: \(url.lastPathComponent)\n") }
            return .success(())
        } catch {
            let errorMessage = "❌ Failed to download template: \(error.localizedDescription)"
            if DEBUG_LOGGING { outputSubject.send("\(errorMessage)\n") }
            return .failure(errorMessage)
        }
    }

    private func createUpdateTemplateContent() -> String {
        return """
Title*,Label Title,Qty,speed,resdef*,Pri,resourceID*,res_link,resdef_link,location,category_id
Updated Resource Title,Updated Label,1,USB3_5,12345,3,67890,rdar://res/67890,rdar://resdef/12345,Aruba,
"""
    }
}

// MARK: - Resource Manager Core
class ResourceManagerCore {

    private let apiBaseURL = "https://radar-webservices.apple.com"
    private let apiVersion = "2.2"
    private var authToken: String?

    // Store the latest processed resources for print functionality
    private var lastProcessedResources: [ProcessedResourceWithExtras] = []

    // Reference to ApplicationService for UI logging
    weak var applicationService: ApplicationService?

    init() {
        // Level 3: Debug logging - core initialization
        if DEBUG_LOGGING { print("🔧 ResourceManagerCore initialized") }
    }

    // MARK: - 3-Level Logging System
    // Level 1: logToUI() - Key process information shown in UI panels (always displayed)
    // Level 2: logToConsole() - Detailed process logs shown in console window (always displayed)
    // Level 3: print() with DEBUG_LOGGING - All debug logs shown in Xcode console (only when DEBUG_LOGGING = true)

    // Level 1: Log key process information to UI (always shown in both UI and console)
    private func logToUI(_ message: String) {
        // Send important messages directly to UI text views (bypassing filtered publisher system)
        Task { @MainActor in
            // Get the main window and view controller
            if let mainWindow = NSApp.mainWindow,
               let viewController = mainWindow.contentViewController as? ViewController {

                // Add timestamp like logUpdateMessage does
                let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .medium)
                let logEntry = "[\(timestamp)] \(message)\n"
                let attributedString = NSAttributedString(string: logEntry)

                // Send to Resource Update panel (primary target for resource operations)
                viewController.updateLogTextView?.textStorage?.append(attributedString)
                viewController.updateLogTextView?.scrollToEndOfDocument(nil)

                // Also send to Create Resource panel for create operations
                viewController.logTextView?.textStorage?.append(attributedString)
                viewController.logTextView?.scrollToEndOfDocument(nil)
            }
        }
        // Also send to console window so console shows all UI messages
        if let appDelegate = NSApp.delegate as? AppDelegate {
            appDelegate.addToConsoleLog(message)
        }
    }

    // Level 2: Log detailed process information to console only (console window only)
    private func logToConsole(_ message: String) {
        // Send detailed logs only to console window (not UI to avoid clutter)
        if let appDelegate = NSApp.delegate as? AppDelegate {
            appDelegate.addToConsoleLog(message)
        }
    }
    
    // MARK: - Public Methods
    
    func createResource(
        title: String?,
        resdefId: String?,
        priority: Int = 5,
        quantity: Int = 1,
        location: String? = nil,
        categoryId: String? = nil
    ) async throws -> [ResourceCreationResult] {
        // Process title and resdef like Swift version
        // Level 1: UI logging - key process information
        logToUI("🔄 Processing resource data...")
        
        // Level 2: Console logging - detailed input parameters
        logToConsole("📥 Input parameters:")
        logToConsole("   title: \(title ?? "nil")")
        logToConsole("   resdefId: \(resdefId ?? "nil")")
        logToConsole("   priority: \(priority)")
        logToConsole("   quantity: \(quantity)")
        logToConsole("   location: \(location ?? ResourceManagerConfig.shared.specificLocation)")
        logToConsole("   categoryId: \(categoryId ?? "nil")")

        // Level 3: Debug logging - detailed input parameters
        if DEBUG_LOGGING { print("🚀 Creating \(quantity) resource(s)...") }
        if DEBUG_LOGGING { print("📥 Input parameters:") }
        if DEBUG_LOGGING { print("   title: \(title ?? "nil")") }
        if DEBUG_LOGGING { print("   resdefId: \(resdefId ?? "nil")") }
        if DEBUG_LOGGING { print("   priority: \(priority)") }
        if DEBUG_LOGGING { print("   quantity: \(quantity)") }
        if DEBUG_LOGGING { print("   location: \(location ?? ResourceManagerConfig.shared.specificLocation)") }
        if DEBUG_LOGGING { print("   categoryId: \(categoryId ?? "nil")") }
        if DEBUG_LOGGING { print("🔄 About to call processResourceData...") }
        
        let processedData = try await processResourceData(
            title: title,
            resdefId: resdefId,
            priority: priority,
            location: location ?? ResourceManagerConfig.shared.specificLocation,
            categoryId: categoryId
        )
        logToUI("✅ Resource data processed successfully")
        if DEBUG_LOGGING { print("✅ processResourceData completed successfully") }

        // Create resources based on quantity
        var createdResources: [ProcessedResourceWithExtras] = []
        var resourceIds: [Int] = []

        // Level 1: UI logging - creation progress
        logToUI("🔄 Creating \(quantity) resource(s)...")

        for i in 1...quantity {
            // Level 1: UI logging - individual resource progress
            if quantity > 1 {
                logToUI("📝 Creating resource \(i) of \(quantity)...")
            }

            // Level 3: Debug logging - individual resource creation
            if DEBUG_LOGGING { print("📝 Creating resource \(i) of \(quantity)...") }

            let resourceId = try await createSingleResource(from: processedData)
            resourceIds.append(resourceId)

            // Level 1: UI logging - individual resource success
            logToUI("✅ Created resource \(resourceId)")

            // Create ProcessedResourceWithExtras for print functionality
            let (cleanedTitle, speedResdef) = cleanTitle(processedData.title)
            let speed = extractSpeedFromTitle(speedResdef)

            let processedResource = ProcessedResourceWithExtras(
                resourceId: String(resourceId),
                resourceLink: "rdar://res/\(resourceId)",
                title: processedData.title,
                priority: processedData.priority,
                resdefId: processedData.resdefId,
                location: processedData.location,
                found: true,
                labelTitle: cleanedTitle,
                speed: speed
            )
            createdResources.append(processedResource)
        }

        // Store created resources for print functionality
        lastProcessedResources.append(contentsOf: createdResources)
        // Level 2: Console logging - storage confirmation
        logToConsole("💾 Stored \(createdResources.count) created resources for print functionality")
        // Level 3: Debug logging - storage confirmation
        if DEBUG_LOGGING { print("💾 Stored \(createdResources.count) created resources for print functionality") }

        // Return results for all created resources
        var results: [ResourceCreationResult] = []
        for resourceId in resourceIds {
            let resourceLink = "rdar://res/\(resourceId)"
            let result = ResourceCreationResult(
                resourceId: resourceId,
                resourceLink: resourceLink,
                title: processedData.title,
                priority: processedData.priority,
                location: processedData.location
            )
            results.append(result)
        }

        // Level 1: UI logging - completion summary
        logToUI("✅ Successfully created \(results.count) resource(s)")

        // Level 2: Console logging - detailed results
        logToConsole("📊 Creation summary:")
        for result in results {
            logToConsole("   Resource ID: \(result.resourceId)")
            logToConsole("   Link: \(result.resourceLink ?? "N/A")")
            logToConsole("   Title: \(result.title ?? "N/A")")
            logToConsole("   Priority: \(result.priority ?? 0)")
            logToConsole("   Location: \(result.location ?? "N/A")")
        }

        // Level 3: Debug logging - completion summary
        if DEBUG_LOGGING { print("✅ Created \(results.count) resources successfully") }
        return results
    }
    
    func createResourcesFromCSV(fileURL: URL) async throws -> [ResourceCreationResult] {
        // Level 1: UI logging - key process information
        logToUI("📁 Creating resources from CSV: \(fileURL.lastPathComponent)")

        // Level 2: Console logging - detailed file information
        logToConsole("📍 Full CSV path: \(fileURL.path)")

        // Level 3: Debug logging - CSV file processing
        if DEBUG_LOGGING { print("📁 Creating resources from CSV: \(fileURL.lastPathComponent)") }

        // Level 1: UI logging - file reading progress
        logToUI("📖 Reading CSV file...")

        // Read CSV file
        let csvContent = try String(contentsOf: fileURL)
        let lines = csvContent.components(separatedBy: .newlines).filter { !$0.isEmpty }

        guard lines.count > 1 else {
            logToUI("❌ CSV validation failed: file must contain at least a header and one data row")
            throw ResourceManagerError.invalidCSV("CSV file must contain at least a header and one data row")
        }

        // Level 1: UI logging - CSV structure validation
        logToUI("📊 Found \(lines.count) lines in CSV (including header)")
        logToUI("📋 Validating CSV structure...")

        // Parse header
        let header = lines[0].components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespaces) }

        // Level 2: Console logging - header information
        logToConsole("📋 CSV Headers: \(header)")

        logToUI("✅ CSV structure validation passed")

        var results: [ResourceCreationResult] = []
        let dataRows = lines.dropFirst()

        // Level 1: UI logging - processing progress
        logToUI("🔄 Processing \(dataRows.count) data rows...")

        // Process each data row
        for (index, line) in dataRows.enumerated() {
            let values = line.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespaces) }

            guard values.count == header.count else {
                // Level 1: UI logging - validation errors
                logToUI("⚠️ Skipping row \(index + 2): column count mismatch")
                // Level 3: Debug logging - CSV validation errors
                if DEBUG_LOGGING { print("⚠️ Skipping row \(index + 2): column count mismatch") }
                continue
            }

            // Level 1: UI logging - individual row processing
            logToUI("🔄 Processing row \(index + 1)/\(dataRows.count)...")

            // Create dictionary from header and values
            let rowData = Dictionary(uniqueKeysWithValues: zip(header, values))

            // Extract values
            let title = rowData["Title*"]?.isEmpty == false ? rowData["Title*"] : nil
            let resdefId = rowData["resdef*"]?.isEmpty == false ? rowData["resdef*"] : nil
            let priority = Int(rowData["Pri"] ?? "5") ?? 5
            let location = (rowData["location"]?.isEmpty == false ? rowData["location"] : nil) ?? "Aruba"
            let categoryId = rowData["category_id"]?.isEmpty == false ? rowData["category_id"] : nil

            // Level 2: Console logging - row details
            logToConsole("📝 Row \(index + 2) data:")
            logToConsole("   Title: \(title ?? "nil")")
            logToConsole("   ResdefId: \(resdefId ?? "nil")")
            logToConsole("   Priority: \(priority)")
            logToConsole("   Location: \(location)")

            do {
                let result = try await createResource(
                    title: title,
                    resdefId: resdefId,
                    priority: priority,
                    quantity: 1,
                    location: location,
                    categoryId: categoryId
                )
                results.append(contentsOf: result)

                // Level 1: UI logging - individual success
                for createdResource in result {
                    logToUI("✅ Created resource \(createdResource.resourceId) from row \(index + 2)")
                }

                if DEBUG_LOGGING {
                    for createdResource in result {
                        print("✅ Created resource \(createdResource.resourceId) from row \(index + 2)")
                    }
                }
            } catch {
                // Level 1: UI logging - creation errors
                logToUI("❌ Failed to create resource from row \(index + 2): \(error.localizedDescription)")
                // Level 3: Debug logging - creation errors
                if DEBUG_LOGGING { print("❌ Failed to create resource from row \(index + 2): \(error)") }
                // Continue with next row
            }
        }

        // Level 1: UI logging - completion summary
        logToUI("📊 CSV processing complete: \(results.count) resources created successfully")

        // Level 2: Console logging - detailed summary
        logToConsole("📊 CSV Creation Summary:")
        logToConsole("   Total rows processed: \(dataRows.count)")
        logToConsole("   Resources created: \(results.count)")
        logToConsole("   Success rate: \(dataRows.count > 0 ? String(format: "%.1f", Double(results.count) / Double(dataRows.count) * 100) : "0")%")

        return results
    }

    func updateResourcesFromCSV(fileURL: URL) async throws -> [ResourceCreationResult] {
        // Level 3: Debug logging - CSV file processing
        if DEBUG_LOGGING { print("📁 Updating resources from CSV: \(fileURL.lastPathComponent)") }
        if DEBUG_LOGGING { print("📍 Full path: \(fileURL.path)") }

        // Ensure we have authentication
        logToConsole("🔐 Checking authentication...")
        try await ensureAuthenticated()
        logToConsole("✅ Authentication verified")

        // Read CSV file
        logToUI("📖 Reading CSV file...")
        let csvContent = try String(contentsOf: fileURL)
        let lines = csvContent.components(separatedBy: .newlines).filter { !$0.isEmpty }

        // Level 3: Debug logging - CSV structure info
        if DEBUG_LOGGING { print("📊 Found \(lines.count) lines in CSV (including header)") }

        guard lines.count > 1 else {
            throw ResourceManagerError.invalidCSV("CSV file must contain at least a header and one data row")
        }

        // Parse header
        let header = lines[0].components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespaces) }
        // Level 3: Debug logging - CSV headers
        if DEBUG_LOGGING { print("📋 CSV Headers: \(header)") }
        // Level 1: UI logging - validation progress
        logToUI("📋 Validating CSV structure...")

        // Validate required columns
        guard header.contains("resourceID*") || header.contains("resourceID") else {
            logToUI("❌ CSV validation failed: missing resourceID column")
            throw ResourceManagerError.invalidCSV("CSV must contain 'resourceID*' or 'resourceID' column")
        }

        logToUI("✅ CSV structure validation passed")

        var results: [ResourceCreationResult] = []
        var processedResources: [ProcessedResourceWithExtras] = []
        let dataRows = lines.dropFirst()
        // Level 3: Debug logging - processing details
        if DEBUG_LOGGING { print("🔄 Processing \(dataRows.count) data rows...") }
        // Level 1: UI logging - key progress information
        logToUI("🔄 Processing \(dataRows.count) data rows...")

        // Process each data row
        for (index, line) in dataRows.enumerated() {
            let values = line.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespaces) }

            guard values.count == header.count else {
                // Level 3: Debug logging - CSV validation errors
                if DEBUG_LOGGING { print("⚠️ Skipping row \(index + 2): column count mismatch (expected \(header.count), got \(values.count))") }
                continue
            }

            // Create dictionary from header and values
            let rowData = Dictionary(uniqueKeysWithValues: zip(header, values))

            // Extract values - for update we need resourceID*
            guard let resourceIdString = rowData["resourceID*"] ?? rowData["resourceID"],
                  !resourceIdString.isEmpty,
                  let resourceId = Int(resourceIdString.replacingOccurrences(of: "rdar://res/", with: "")) else {
                // Level 3: Debug logging - invalid resource ID
                if DEBUG_LOGGING { print("⚠️ Skipping row \(index + 2): missing or invalid resourceID (\(rowData["resourceID*"] ?? rowData["resourceID"] ?? "nil"))") }
                continue
            }

            let title = rowData["Title*"]?.isEmpty == false ? rowData["Title*"] : nil
            let priorityString = rowData["Pri"]?.isEmpty == false ? rowData["Pri"] : nil
            let priority = priorityString != nil ? Int(priorityString!) : nil
            let location = (rowData["location"]?.isEmpty == false ? rowData["location"] : nil) ?? "Aruba"

            // Level 3: Debug logging - detailed row processing info
            if DEBUG_LOGGING { print("🔄 Processing row \(index + 2): Resource ID \(resourceId)") }

            // Level 1: UI logging - key progress information
            logToUI("🔄 Processing resource \(index + 1)/\(dataRows.count): ID \(resourceId)")

            // Level 2: Console logging - detailed process information
            logToConsole("Processing resource details:")
            if let title = title {
                logToConsole("   Title: \(title)")
            }
            logToConsole("   Priority: \(priority?.description ?? "nil (empty)")")
            logToConsole("   Location: \(location)")

            do {
                // First, get the current resource information from API
                // Level 3: Debug logging - API call details
                if DEBUG_LOGGING { print("🔍 Fetching current resource information for \(resourceId)...") }
                // Level 1: UI logging - key progress information
                logToUI("🔍 Fetching current resource information for \(resourceId)...")
                let currentResource = try await getResourceAPI(resourceId: resourceId)
                logToUI("✅ Retrieved current resource data")

                // Merge CSV updates with current resource data
                let updatedTitle = (title?.isEmpty == false) ? title! : currentResource.title
                var updatedPriority = priority ?? currentResource.priority
                let updatedLocation = (rowData["location"]?.isEmpty == false) ? location : currentResource.location

                // For CSV UPDATE operations: If priority is nil (empty), try to get it from Keywords API
                if priority == nil {
                    logToUI("🔍 CSV Priority is empty, attempting to extract from Keywords API...")

                    // Try to extract resdefId from title or CSV
                    var resdefId: String? = nil
                    let csvResdefId = rowData["resdef*"]
                    if let resdefId_csv = csvResdefId, !resdefId_csv.isEmpty {
                        resdefId = resdefId_csv
                    } else {
                        resdefId = extractResdefId(from: updatedTitle)
                        if resdefId == nil {
                            resdefId = currentResource.resdefId
                        }
                    }

                    if let resdefIdString = resdefId, let resdefIdInt = Int(resdefIdString) {
                        // Level 3: Debug logging - priority extraction details
                        if DEBUG_LOGGING { print("🔍 Using resdefId \(resdefIdInt) to extract priority...") }
                        do {
                            let (extractedPriority, _) = try await extractTitlePriority(resdefId: resdefIdInt)

                            if let extractedPriority = extractedPriority {
                                updatedPriority = extractedPriority
                                if DEBUG_LOGGING { print("✅ Using priority from Keywords API: \(extractedPriority)") }
                            } else {
                                if DEBUG_LOGGING { print("⚠️ No priority found in Keywords API, keeping current priority: \(currentResource.priority)") }
                            }
                        } catch {
                            if DEBUG_LOGGING { print("⚠️ Failed to extract priority from Keywords API: \(error), keeping current priority: \(currentResource.priority)") }
                        }
                    } else {
                        if DEBUG_LOGGING { print("⚠️ No resdefId available, keeping current priority: \(currentResource.priority)") }
                    }
                } else {
                    if DEBUG_LOGGING { print("🔢 Using CSV provided priority: \(priority!)") }
                }

                // Level 2: Console logging - detailed comparison information
                logToConsole("Resource comparison:")
                logToConsole("   Current: title='\(currentResource.title)', priority=\(currentResource.priority), location='\(currentResource.location)'")
                logToConsole("   Updated: title='\(updatedTitle)', priority=\(updatedPriority), location='\(updatedLocation)'")

                // Update the resource with merged data
                logToUI("🔄 Calling API to update resource \(resourceId)...")
                try await updateResourceAPI(
                    resourceId: resourceId,
                    title: updatedTitle,
                    priority: updatedPriority,
                    quantity: 1,
                    location: updatedLocation
                )
                logToUI("✅ Successfully updated resource \(resourceId)")

                // Log detailed resource information in real-time
                logToUI("      Title: \(updatedTitle)")
                logToConsole("      Priority: \(updatedPriority)")
                logToConsole("      Location: \(updatedLocation)")
                logToConsole("      Link: rdar://res/\(resourceId)")

                // Create ResourceCreationResult for return value
                let result = ResourceCreationResult(
                    resourceId: resourceId,
                    resourceLink: "rdar://res/\(resourceId)",
                    title: updatedTitle,
                    priority: updatedPriority,
                    location: updatedLocation
                )
                results.append(result)

                // Create ProcessedResourceWithExtras for print functionality using complete resource data
                let fullTitle = updatedTitle
                let (cleanedTitle, speedResdef) = cleanTitle(fullTitle)
                let speed = extractSpeedFromTitle(speedResdef)

                // Get resdef ID - prioritize CSV column, then extract from title, then use current resource
                var finalResdefId: String? = nil
                let csvResdefId = rowData["resdef*"]
                if let resdefId = csvResdefId, !resdefId.isEmpty {
                    finalResdefId = resdefId
                    // Level 3: Debug logging - CSV resdef usage
                    if DEBUG_LOGGING { print("   📋 Using CSV resdef*: \(resdefId)") }
                } else {
                    finalResdefId = extractResdefId(from: fullTitle)
                    if let extracted = finalResdefId {
                        // Level 3: Debug logging - extracted resdef
                        if DEBUG_LOGGING { print("   🔍 Extracted resdef from title: \(extracted)") }
                    } else {
                        finalResdefId = currentResource.resdefId
                        // Level 3: Debug logging - current resource resdef
                        if DEBUG_LOGGING { print("   📄 Using current resource resdef: \(finalResdefId ?? "nil")") }
                    }
                }

                // Level 2: Console logging - detailed processed resource data
                logToConsole("ProcessedResource data:")
                logToConsole("   Title: \(fullTitle)")
                logToConsole("   Label Title: \(cleanedTitle)")
                logToConsole("   Speed: \(speed)")
                logToConsole("   Resdef ID: \(finalResdefId ?? "nil")")
                logToConsole("   Location: \(updatedLocation)")

                let processedResource = ProcessedResourceWithExtras(
                    resourceId: String(resourceId),
                    resourceLink: "rdar://res/\(resourceId)",
                    title: fullTitle,
                    priority: updatedPriority,
                    resdefId: finalResdefId,
                    location: updatedLocation,
                    found: true,
                    labelTitle: cleanedTitle,
                    speed: speed
                )
                processedResources.append(processedResource)

                // Level 3: Debug logging - successful update
                if DEBUG_LOGGING { print("✅ Updated resource \(resourceId) from row \(index + 2)") }
            } catch {
                // Level 3: Debug logging - update errors
                if DEBUG_LOGGING { print("❌ Failed to update resource \(resourceId) from row \(index + 2): \(error)") }
                // Level 1: UI logging - error notification
                logToUI("❌ Failed to update resource \(resourceId): \(error.localizedDescription)")
                // Continue with next row instead of failing completely
            }
        }

        // Level 3: Debug logging - update summary
        if DEBUG_LOGGING { print("📊 Update summary: \(results.count) out of \(dataRows.count) resources updated successfully") }
        // Level 1: UI logging - final summary
        logToUI("📊 Update summary: \(results.count) out of \(dataRows.count) resources updated successfully")

        // Store processed resources for print functionality
        lastProcessedResources = processedResources
        // Level 3: Debug logging - storage confirmation
        if DEBUG_LOGGING { print("💾 Stored \(processedResources.count) processed resources for print functionality") }

        // Level 3: Debug logging - detailed processed resources
        for (index, resource) in processedResources.enumerated() {
            if DEBUG_LOGGING { print("   Resource \(index + 1):") }
            if DEBUG_LOGGING { print("      ID: \(resource.resourceId)") }
            if DEBUG_LOGGING { print("      Title: \(resource.title)") }
            if DEBUG_LOGGING { print("      Label Title: \(resource.labelTitle)") }
            if DEBUG_LOGGING { print("      Speed: \(resource.speed)") }
            if DEBUG_LOGGING { print("      Resdef ID: \(resource.resdefId ?? "nil")") }
            if DEBUG_LOGGING { print("      Location: \(resource.location ?? "nil")") }
        }

        return results
    }

    func updateSingleResource(
        resourceId: Int,
        title: String?,
        priority: Int?,
        location: String
    ) async throws -> ResourceCreationResult {

        // Level 1: UI logging - key progress information
        logToUI("🔄 Updating single resource \(resourceId)...")
        // Level 2: Console logging - detailed input parameters
        logToConsole("📥 Input parameters:")
        logToConsole("   resourceId: \(resourceId)")
        logToConsole("   title: \(title ?? "nil")")
        logToConsole("   priority: \(priority?.description ?? "nil (empty)")")
        logToConsole("   location: \(location)")

        // Ensure we have authentication
        try await ensureAuthenticated()

        // First, get the current resource information from API
        // Level 1: UI logging - key progress information
        logToUI("🔍 Fetching current resource information for \(resourceId)...")
        let currentResource = try await getResourceAPI(resourceId: resourceId)
        logToUI("✅ Retrieved current resource data")

        // Merge updates with current resource data
        let updatedTitle = (title?.isEmpty == false) ? title! : currentResource.title
        var updatedPriority = priority ?? currentResource.priority
        let updatedLocation = location

        // For UPDATE operations: If priority is nil (empty), try to get it from Keywords API
        if priority == nil {
            // Level 1: UI logging - key process information
            logToUI("🔍 Priority is empty, attempting to extract from Keywords API...")

            // Try to extract resdefId from title
            var resdefId: String? = nil
            resdefId = extractResdefId(from: updatedTitle)
            if resdefId == nil {
                resdefId = currentResource.resdefId
            }

            if let resdefIdString = resdefId, let resdefIdInt = Int(resdefIdString) {
                // Level 2: Console logging - detailed process information
                logToConsole("🔍 Using resdefId \(resdefIdInt) to extract priority...")
                do {
                    let (extractedPriority, _) = try await extractTitlePriority(resdefId: resdefIdInt)

                    if let extractedPriority = extractedPriority {
                        updatedPriority = extractedPriority
                        // Level 1: UI logging - key process information
                        logToUI("✅ Using priority from Keywords API: \(extractedPriority)")
                    } else {
                        updatedPriority = 5  // Default to 5 if no priority found in Keywords API
                        // Level 1: UI logging - key process information
                        logToUI("⚠️ No priority found in Keywords API, using default priority: 5")
                    }
                } catch {
                    // Level 1: UI logging - key process information
                    logToConsole("⚠️ Failed to extract priority from Keywords API: \(error), keeping current priority: \(currentResource.priority)")
                }
            } else {
                // Level 2: Console logging - detailed process information
                logToConsole("⚠️ No resdefId available, keeping current priority: \(currentResource.priority)")
            }
        } else {
            // Level 2: Console logging - detailed process information
            logToConsole("🔢 Using provided priority: \(priority!)")
        }

        // Level 2: Console logging - detailed comparison information
        logToConsole("   Current: title='\(currentResource.title)', priority=\(currentResource.priority), location='\(currentResource.location)'")
        logToConsole("   Updated: title='\(updatedTitle)', priority=\(updatedPriority)', location='\(updatedLocation)'")

        // Update the resource with merged data
        try await updateResourceAPI(
            resourceId: resourceId,
            title: updatedTitle,
            priority: updatedPriority,
            quantity: 1,
            location: updatedLocation
        )

        // Create ProcessedResourceWithExtras for print functionality using complete resource data
        let fullTitle = updatedTitle
        let (cleanedTitle, speedResdef) = cleanTitle(fullTitle)
        let speed = extractSpeedFromTitle(speedResdef)

        // Get resdef ID - extract from title or use current resource
        var finalResdefId: String? = nil
        finalResdefId = extractResdefId(from: fullTitle)
        if finalResdefId == nil {
            finalResdefId = currentResource.resdefId
        }

        // Level 3: Debug logging - detailed processed resource data
        if DEBUG_LOGGING { print("   📝 ProcessedResource data:") }
        if DEBUG_LOGGING { print("      Title: \(fullTitle)") }
        if DEBUG_LOGGING { print("      Label Title: \(cleanedTitle)") }
        if DEBUG_LOGGING { print("      Speed: \(speed)") }
        if DEBUG_LOGGING { print("      Resdef ID: \(finalResdefId ?? "nil")") }
        if DEBUG_LOGGING { print("      Location: \(updatedLocation)") }

        let processedResource = ProcessedResourceWithExtras(
            resourceId: String(resourceId),
            resourceLink: "rdar://res/\(resourceId)",
            title: fullTitle,
            priority: updatedPriority,
            resdefId: finalResdefId,
            location: updatedLocation,
            found: true,
            labelTitle: cleanedTitle,
            speed: speed
        )

        // Store processed resource for print functionality
        lastProcessedResources = [processedResource]
        // Level 3: Debug logging - storage confirmation
        if DEBUG_LOGGING { print("💾 Stored 1 processed resource for print functionality") }

        // Level 3: Debug logging - completion confirmation
        if DEBUG_LOGGING { print("✅ Updated resource \(resourceId) successfully") }

        // Return result for UI display
        return ResourceCreationResult(
            resourceId: resourceId,
            resourceLink: "rdar://res/\(resourceId)",
            title: updatedTitle,
            priority: updatedPriority,
            location: updatedLocation
        )
    }

    func getLastProcessedResources() -> [ProcessedResourceWithExtras] {
        return lastProcessedResources
    }

    func printResources() async throws -> String {
        // Level 3: Debug logging - print operation start
        if DEBUG_LOGGING { print("🖨️ Generating print resources file...") }

        // Use the latest processed resources, or sample data if none exist
        let resourcesToPrint = lastProcessedResources.isEmpty ? createSampleResources() : lastProcessedResources
        // Level 3: Debug logging - resource selection
        if DEBUG_LOGGING { print("📊 Using \(resourcesToPrint.count) resources for print (\(lastProcessedResources.isEmpty ? "sample" : "from last update"))") }

        // Generate timestamp for filename
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .short, timeStyle: .short)
        let filename = "ResourcePrint_\(timestamp.replacingOccurrences(of: "/", with: "-").replacingOccurrences(of: ":", with: "-")).csv"

        let downloadsURL = FileManager.default.urls(for: .downloadsDirectory, in: .userDomainMask).first!
        let outputURL = downloadsURL.appendingPathComponent(filename)

        // Generate CSV content from processed resources
        let csvContent = generatePrintContent(from: resourcesToPrint)

        try csvContent.write(to: outputURL, atomically: true, encoding: .utf8)

        // Level 3: Debug logging - file save confirmation
        if DEBUG_LOGGING { print("✅ Print file saved: \(filename)") }
        return outputURL.path
    }
    
    // MARK: - Private Methods
    
    private func ensureAuthenticated() async throws {
        if authToken == nil {
            authToken = try await authenticate()
        }
    }
    
    private func authenticate() async throws -> String {
        // Level 3: Debug logging - authentication start
        if DEBUG_LOGGING { print("🔐 Authenticating with Kerberos...") }

        let url = URL(string: "\(apiBaseURL)/signon")!
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.setValue("application/json", forHTTPHeaderField: "Accept")
        request.setValue(apiVersion, forHTTPHeaderField: "X-API-Version")

        // Level 3: Debug logging - request details
        if DEBUG_LOGGING { print("📤 Auth request URL: \(url)") }
        if DEBUG_LOGGING { print("📤 Auth request headers: \(request.allHTTPHeaderFields ?? [:])") }

        // Create a URLSession with authentication delegate to handle Kerberos
        let delegate = AuthenticationDelegate()
        let session = URLSession(configuration: .default, delegate: delegate, delegateQueue: nil)

        let (data, response) = try await session.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            // Level 3: Debug logging - response validation error
            if DEBUG_LOGGING { print("❌ Invalid response type") }
            throw ResourceManagerError.networkError("Invalid response type")
        }

        // Level 3: Debug logging - response details
        if DEBUG_LOGGING { print("📥 Auth response status: \(httpResponse.statusCode)") }
        if DEBUG_LOGGING { print("📥 Auth response headers: \(httpResponse.allHeaderFields)") }

        if let responseString = String(data: data, encoding: .utf8) {
            if DEBUG_LOGGING { print("📥 Auth response body: \(responseString)") }
        }

        guard httpResponse.statusCode == 200 else {
            // Level 3: Debug logging - authentication failure
            if DEBUG_LOGGING { print("❌ Authentication failed with status \(httpResponse.statusCode)") }

            // Special handling for 401 - Kerberos authentication issue
            if httpResponse.statusCode == 401 {
                throw ResourceManagerError.kerberosAuthenticationError("Kerberos authentication failed. Please run 'kinit <EMAIL>' to obtain a valid Kerberos ticket.")
            } else {
                throw ResourceManagerError.authenticationError("Authentication failed with status \(httpResponse.statusCode)")
            }
        }

        // Parse authentication response
        let authResponse = try JSONSerialization.jsonObject(with: data) as? [String: Any]
        // Level 3: Debug logging - response parsing
        if DEBUG_LOGGING { print("📥 Auth response parsed: \(authResponse ?? [:])") }

        guard let token = authResponse?["accessToken"] as? String else {
            // Level 3: Debug logging - token extraction error
            if DEBUG_LOGGING { print("❌ No access token in response") }
            throw ResourceManagerError.authenticationError("No access token in response")
        }

        // Level 3: Debug logging - authentication success
        if DEBUG_LOGGING { print("✅ Authentication successful, token length: \(token.count)") }
        return token
    }
    
    private func performCreateRequest(body: [String: Any]) async throws -> Int {
        let url = URL(string: "\(apiBaseURL)/tests/resources")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("application/json", forHTTPHeaderField: "Accept")
        request.setValue(apiVersion, forHTTPHeaderField: "X-API-Version")
        
        if let token = authToken {
            request.setValue(token, forHTTPHeaderField: "Radar-Authentication")
        }
        
        request.httpBody = try JSONSerialization.data(withJSONObject: body)
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw ResourceManagerError.networkError("Invalid response type")
        }
        
        // Level 3: Debug logging - response status
        if DEBUG_LOGGING { print("📥 Create response status: \(httpResponse.statusCode)") }

        guard httpResponse.statusCode == 201 else {
            // Log detailed error information for debugging
            let errorMsg1 = "❌ Create request failed with status \(httpResponse.statusCode)"
            let errorMsg2 = "📤 Request body was: \(body)"
            // Level 3: Debug logging - create request error details
            print(errorMsg1)
            print(errorMsg2)

            // Try to get error details from response
            if let errorData = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
                let errorMsg3 = "📥 Error response: \(errorData)"
                // Level 3: Debug logging - error response data
                print(errorMsg3)
            } else if let errorString = String(data: data, encoding: .utf8) {
                let errorMsg4 = "📥 Error response (raw): \(errorString)"
                // Level 3: Debug logging - raw error response
                print(errorMsg4)
            }

            throw ResourceManagerError.serverError("Create failed with status \(httpResponse.statusCode)")
        }
        
        // Parse response
        let responseData = try JSONSerialization.jsonObject(with: data) as? [String: Any]
        guard let resourceId = responseData?["id"] as? Int else {
            throw ResourceManagerError.serverError("No resource ID in response")
        }
        
        // Level 3: Debug logging - creation success
        if DEBUG_LOGGING { print("✅ Resource created with ID: \(resourceId)") }
        return resourceId
    }
    
    private func attachResourceToResdef(resourceId: Int, resdefId: Int) async throws {
        // Level 3: Debug logging - attachment start
        if DEBUG_LOGGING { print("🔗 Attaching resource \(resourceId) to resdef \(resdefId)") }
        
        let url = URL(string: "\(apiBaseURL)/tests/resourcedefinitions/\(resdefId)")!
        var request = URLRequest(url: url)
        request.httpMethod = "PUT"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("application/json", forHTTPHeaderField: "Accept")
        request.setValue(apiVersion, forHTTPHeaderField: "X-API-Version")
        
        if let token = authToken {
            request.setValue(token, forHTTPHeaderField: "Radar-Authentication")
        }
        
        let requestBody: [String: Any] = [
            "resources": [
                "upsert": [
                    [
                        "id": resourceId,
                        "include": true
                    ]
                ]
            ]
        ]
        
        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        
        let (_, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw ResourceManagerError.networkError("Invalid response type")
        }
        
        // Level 3: Debug logging - response status
        if DEBUG_LOGGING { print("📥 Attach response status: \(httpResponse.statusCode)") }

        // Accept both 200 and 205 as success
        guard [200, 205].contains(httpResponse.statusCode) else {
            // Level 3: Debug logging - attachment failure
            if DEBUG_LOGGING { print("⚠️ Attach failed with status \(httpResponse.statusCode), continuing...") }
            return
        }
        
        // Level 3: Debug logging - attachment success
        if DEBUG_LOGGING { print("✅ Resource attached to resdef successfully") }
    }

    private func processResourceData(
        title: String?,
        resdefId: String?,
        priority: Int,
        location: String,
        categoryId: String?
    ) async throws -> ProcessedResourceData {

        var finalTitle = title ?? ""
        var finalResdefId = resdefId ?? ""
        var finalPriority = priority

        // Level 3: Debug logging - processing start
        if DEBUG_LOGGING { print("🔍 Processing resource data...") }
        if DEBUG_LOGGING { print("   Input - Title: '\(finalTitle)', ResdefId: '\(finalResdefId)', Priority: \(priority), CategoryId: '\(categoryId ?? "nil")'") }
        if DEBUG_LOGGING { print("   Title isEmpty: \(finalTitle.isEmpty), ResdefId isEmpty: \(finalResdefId.isEmpty)") }

        // If title is empty, get it from resource definition (like Python version)
        if finalTitle.isEmpty && !finalResdefId.isEmpty {
            // Level 3: Debug logging - title fetching
            if DEBUG_LOGGING { print("📝 Title is empty, fetching from resdef API...") }
            do {
                let resdefIdInt = Int(finalResdefId) ?? 0
                if DEBUG_LOGGING { print("🔢 Converting resdefId '\(finalResdefId)' to int: \(resdefIdInt)") }
                let resourceDef = try await getResourceDefinitionAPI(id: resdefIdInt)
                finalTitle = resourceDef.title + " (resdef: \(finalResdefId))"
                // Level 3: Debug logging - title retrieval success
                if DEBUG_LOGGING { print("✅ Got title from resdef API: \(finalTitle)") }
            } catch {
                // Level 3: Debug logging - title retrieval failure
                if DEBUG_LOGGING { print("⚠️ Failed to get resource definition from API: \(error)") }
                finalTitle = "Resource with resdef \(finalResdefId)"
            }
        } else {
            // Level 3: Debug logging - condition check
            if DEBUG_LOGGING { print("🔍 Condition not met - Title isEmpty: \(finalTitle.isEmpty), ResdefId isEmpty: \(finalResdefId.isEmpty)") }
        }

        // Extract resdefId from title if not provided (like Python version)
        if finalResdefId.isEmpty && !finalTitle.isEmpty {
            // Level 3: Debug logging - resdef extraction
            if DEBUG_LOGGING { print("🔍 Extracting resdefId from title...") }
            finalResdefId = extractResdefId(from: finalTitle) ?? ""
            if !finalResdefId.isEmpty {
                if DEBUG_LOGGING { print("✅ Extracted resdefId: \(finalResdefId)") }
            }
        }

        // For CREATE operations: Always try to get priority from Keywords API if we have resdefId
        if !finalResdefId.isEmpty {
            // Level 2: Console logging - detailed process information
            logToConsole("🔍 Attempting to extract priority from Keywords API for resdef \(finalResdefId)...")
            do {
                let resdefIdInt = Int(finalResdefId) ?? 0
                let (extractedPriority, extractedTitle) = try await extractTitlePriority(resdefId: resdefIdInt)

                if let extractedPriority = extractedPriority {
                    finalPriority = extractedPriority
                    // Level 1: UI logging - key process information
                    logToUI("✅ Using priority from Keywords API: \(extractedPriority)")
                } else {
                    finalPriority = 5  // Default to 5 if no priority found in Keywords API
                    // Level 1: UI logging - key process information
                    logToUI("⚠️ No priority found in Keywords API, using default priority: 5")
                }

                // Update title if we got a better one from API
                if finalTitle.isEmpty || finalTitle.contains("Resource with resdef") {
                    finalTitle = extractedTitle + " (resdef: \(finalResdefId))"
                    // Level 2: Console logging - detailed process information
                    logToConsole("✅ Updated title from Keywords API: \(finalTitle)")
                }
            } catch {
                // Level 1: UI logging - key process information
                logToConsole("⚠️ Failed to extract priority from Keywords API: \(error), using provided priority: \(priority)")
                // Keep the provided priority if API call fails
            }
        } else {
            // Level 2: Console logging - detailed process information
            logToConsole("⚠️ No resdefId available, using provided priority: \(priority)")
        }

        // Validate that we have either title or resdefId
        guard !finalTitle.isEmpty || !finalResdefId.isEmpty else {
            throw ResourceManagerError.validationError("Either title or resdefId is required")
        }

        // Level 3: Debug logging - final processed data summary
        if DEBUG_LOGGING { print("📋 Final processed data:") }
        if DEBUG_LOGGING { print("   Title: '\(finalTitle)'") }
        if DEBUG_LOGGING { print("   ResdefId: '\(finalResdefId)'") }
        if DEBUG_LOGGING { print("   CategoryId: '\(categoryId ?? "nil")' (will be determined during creation)") }
        if DEBUG_LOGGING { print("   Priority: \(finalPriority) (from \(finalPriority == priority ? "user input" : "Keywords API"))") }
        if DEBUG_LOGGING { print("   Location: '\(location)'") }

        // Pass through the original categoryId without trying to fetch it here
        // Let createSingleResource handle categoryId logic like Python version
        return ProcessedResourceData(
            title: finalTitle,
            resdefId: finalResdefId.isEmpty ? nil : finalResdefId,
            priority: finalPriority,
            location: location,
            categoryId: categoryId
        )
    }

    private func getResourceAPI(resourceId: Int) async throws -> ResourceData {
        // Level 3: Debug logging - API call start
        if DEBUG_LOGGING { print("📥 Getting resource \(resourceId)...") }

        let url = URL(string: "\(apiBaseURL)/tests/resources/\(resourceId)")!
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.setValue("application/json", forHTTPHeaderField: "Accept")
        request.setValue(apiVersion, forHTTPHeaderField: "X-API-Version")

        if let token = authToken {
            request.setValue(token, forHTTPHeaderField: "Radar-Authentication")
        }

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw ResourceManagerError.networkError("Invalid response type")
        }

        // Level 3: Debug logging - response status
        if DEBUG_LOGGING { print("📥 Get response status: \(httpResponse.statusCode)") }

        guard httpResponse.statusCode == 200 else {
            throw ResourceManagerError.serverError("Get failed with status \(httpResponse.statusCode)")
        }

        // Parse response
        let responseData = try JSONSerialization.jsonObject(with: data) as? [String: Any]
        guard let resourceData = responseData else {
            throw ResourceManagerError.serverError("Invalid response data")
        }

        // Extract resource information
        let title = resourceData["title"] as? String ?? "Unknown Resource"
        let priority = resourceData["priority"] as? Int ?? 5
        let location = resourceData["specificLocation"] as? String ?? "Unknown"
        let resdefId = resourceData["resdefId"] as? String // This might be nil

        // Level 3: Debug logging - resource retrieval success
        if DEBUG_LOGGING { print("✅ Retrieved resource: title='\(title)', priority=\(priority), location='\(location)', resdefId=\(resdefId ?? "nil")") }

        return ResourceData(
            title: title,
            priority: priority,
            location: location,
            resdefId: resdefId
        )
    }

    private func getResourceDefinitionAPI(id: Int) async throws -> ResourceDefinitionData {
        // Level 3: Debug logging - API call start
        if DEBUG_LOGGING { print("📥 Getting resource definition \(id)...") }

        let url = URL(string: "\(apiBaseURL)/tests/resourcedefinitions/\(id)")!
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.setValue("application/json", forHTTPHeaderField: "Accept")
        request.setValue(apiVersion, forHTTPHeaderField: "X-API-Version")

        if let token = authToken {
            request.setValue(token, forHTTPHeaderField: "Radar-Authentication")
        }

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw ResourceManagerError.networkError("Invalid response type")
        }

        // Level 3: Debug logging - response status
        if DEBUG_LOGGING { print("📥 Get resdef response status: \(httpResponse.statusCode)") }

        guard httpResponse.statusCode == 200 else {
            throw ResourceManagerError.serverError("Get resdef failed with status \(httpResponse.statusCode)")
        }

        // Parse response
        let responseData = try JSONSerialization.jsonObject(with: data) as? [String: Any]
        guard let resdefData = responseData else {
            throw ResourceManagerError.serverError("Invalid resdef response data")
        }

        let title = resdefData["title"] as? String ?? "Unknown Resource Definition"
        let categoryId = resdefData["categoryId"] as? Int ?? 1 // Default categoryId if not found

        // Level 3: Debug logging - resource definition retrieval success
        if DEBUG_LOGGING { print("✅ Retrieved resource definition: title='\(title)', categoryId=\(categoryId)") }

        return ResourceDefinitionData(title: title, categoryId: categoryId)
    }

    private func getResourceDefinitionFieldsAPI(id: Int, fields: [String]) async throws -> ResourceDefinitionFieldsData {
        // Level 3: Debug logging - API call start
        if DEBUG_LOGGING { print("📥 Getting resource definition fields for \(id): \(fields)...") }

        // Use X-Fields-Requested header instead of query parameters (like Python version)
        let fieldsParam = fields.joined(separator: ",")
        let url = URL(string: "\(apiBaseURL)/tests/resourcedefinitions/\(id)")!
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.setValue("application/json", forHTTPHeaderField: "Accept")
        request.setValue(apiVersion, forHTTPHeaderField: "X-API-Version")
        request.setValue(fieldsParam, forHTTPHeaderField: "X-Fields-Requested")

        if let token = authToken {
            request.setValue(token, forHTTPHeaderField: "Radar-Authentication")
        }

        // Use the same session with authentication delegate
        let delegate = AuthenticationDelegate()
        let session = URLSession(configuration: .default, delegate: delegate, delegateQueue: nil)

        let (data, response) = try await session.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw ResourceManagerError.networkError("Invalid response type")
        }

        // Level 3: Debug logging - response status
        if DEBUG_LOGGING { print("📥 Get resdef fields response status: \(httpResponse.statusCode)") }

        guard httpResponse.statusCode == 200 else {
            // Level 3: Debug logging - request failure
            if DEBUG_LOGGING { print("❌ Get resdef fields failed with status \(httpResponse.statusCode)") }

            // Special handling for 401 - Kerberos authentication issue
            if httpResponse.statusCode == 401 {
                throw ResourceManagerError.kerberosAuthenticationError("Kerberos authentication failed during resource definition fields retrieval. Please run 'kinit <EMAIL>' to obtain a valid Kerberos ticket.")
            } else {
                throw ResourceManagerError.serverError("Get resdef fields failed with status \(httpResponse.statusCode)")
            }
        }

        // Parse response
        let responseData = try JSONSerialization.jsonObject(with: data) as? [String: Any]
        guard let resdefData = responseData else {
            throw ResourceManagerError.serverError("Invalid resdef fields response data")
        }

        // Level 3: Debug logging - raw response data
        if DEBUG_LOGGING { print("📥 Raw resdef fields response: \(resdefData)") }

        let title = resdefData["title"] as? String ?? "Unknown Resource Definition"
        let keywordsArray = resdefData["keywords"] as? [[String: Any]] ?? []

        // Extract keywords
        var keywords: [String] = []
        for keywordData in keywordsArray {
            if let keywordInfo = keywordData["keyword"] as? [String: Any],
               let keywordName = keywordInfo["name"] as? String {
                keywords.append(keywordName)
            }
        }

        // Level 3: Debug logging - fields retrieval success
        if DEBUG_LOGGING { print("✅ Retrieved resource definition fields: title='\(title)', keywords=\(keywords)") }

        return ResourceDefinitionFieldsData(title: title, keywords: keywords)
    }

    private func extractTitlePriority(resdefId: Int) async throws -> (Int?, String) {
        // Level 1: UI logging - key process information
        logToUI("🔍 Extracting title and priority from resdef \(resdefId)...")

        do {
            let resourceData = try await getResourceDefinitionFieldsAPI(id: resdefId, fields: ["keywords", "title"])
            let title = resourceData.title
            let keywords = resourceData.keywords

            // Level 2: Console logging - detailed process information
            logToConsole("📋 Keywords found: \(keywords)")

            // Pattern to match 'ATC USB' followed by digits and 'P' followed by digits (handles both "ATC USB3 P1" and "ATC USB 3 P 1")
            let patternUSB = #"ATC USB\s*(\d+)\s*P\s*(\d+)"#

            // Pattern to match 'ATC CIO P' followed by digits
            let patternTBT = #"ATC CIO P(\d+)"#

            // Iterate over each keyword
            for keyword in keywords {
                // Level 2: Console logging - detailed process information
                logToConsole("🔍 Checking keyword: '\(keyword)'")

                // Check if the keyword matches the USB pattern
                if let usbMatch = keyword.range(of: patternUSB, options: .regularExpression) {
                    let matchString = String(keyword[usbMatch])
                    // Level 2: Console logging - detailed process information
                    logToConsole("🎯 USB pattern matched: '\(matchString)'")

                    // Extract the P value for USB (group 2)
                    let regex = try NSRegularExpression(pattern: patternUSB)
                    let nsString = keyword as NSString
                    if let match = regex.firstMatch(in: keyword, range: NSRange(location: 0, length: nsString.length)) {
                        let priorityRange = match.range(at: 2) // group 2 for P value
                        if priorityRange.location != NSNotFound {
                            let priorityString = nsString.substring(with: priorityRange)
                            if let priority = Int(priorityString) {
                                let adjustedPriority = priority + 1
                                // Level 1: UI logging - key process information
                                logToUI("✅ Found USB priority: \(priority) in keyword '\(keyword)', using \(adjustedPriority) (priority + 1)")
                                return (adjustedPriority, title)
                            }
                        }
                    }
                }

                // Check if the keyword matches the TBT pattern
                if let tbtMatch = keyword.range(of: patternTBT, options: .regularExpression) {
                    let matchString = String(keyword[tbtMatch])
                    // Level 2: Console logging - detailed process information
                    logToConsole("🎯 TBT pattern matched: '\(matchString)'")

                    // Extract the P value for TBT (group 1)
                    let regex = try NSRegularExpression(pattern: patternTBT)
                    let nsString = keyword as NSString
                    if let match = regex.firstMatch(in: keyword, range: NSRange(location: 0, length: nsString.length)) {
                        let priorityRange = match.range(at: 1) // group 1 for P value
                        if priorityRange.location != NSNotFound {
                            let priorityString = nsString.substring(with: priorityRange)
                            if let priority = Int(priorityString) {
                                let adjustedPriority = priority + 1
                                // Level 1: UI logging - key process information
                                logToUI("✅ Found TBT priority: \(priority) in keyword '\(keyword)', using \(adjustedPriority) (priority + 1)")
                                return (adjustedPriority, title)
                            }
                        }
                    }
                }
            }

            // Level 1: UI logging - key process information
            logToUI("⚠️ No priority pattern found in keywords")
            return (nil, title)

        } catch {
            // Level 1: UI logging - key process information
            logToUI("⚠️ Failed to extract title priority: \(error)")
            throw error
        }
    }

    private func createSingleResource(from processedData: ProcessedResourceData) async throws -> Int {
        // Level 3: Debug logging - creation start
        if DEBUG_LOGGING { print("🚀 Creating single resource...") }

        // Ensure we have authentication
        try await ensureAuthenticated()

        // Use configuration values instead of hardcoded values
        let config = ResourceManagerConfig.shared
        let componentId = config.componentId
        let classId = config.classId
        let stateId = config.stateId
        let locationId = config.locationId
        let driId = config.driId
        let inventoryKeeperId = config.inventoryKeeperId
        // Remove assigneeId as it's not recognized by the API

        // Convert priority to API format (display priority + 1, except for 5 which stays 5)
        let apiPriority = processedData.priority == 5 ? 5 : processedData.priority + 1

        // Create request body with only valid fields
        var requestBody: [String: Any] = [
            "componentId": componentId,
            "classId": classId,
            "stateId": stateId,
            "priority": apiPriority,
            "locationId": locationId,
            "specificLocation": processedData.location,
            "driId": driId,
            "inventoryKeeperId": inventoryKeeperId
            // Removed assigneeId as it causes "Unknown field" error
        ]

        // Add title if provided
        if !processedData.title.isEmpty {
            requestBody["title"] = processedData.title
        }

        // Add categoryId - handle like Python version
        var categoryId: Int
        if let providedCategoryId = processedData.categoryId, let catId = Int(providedCategoryId) {
            categoryId = catId
            // Level 3: Debug logging - category ID usage
            if DEBUG_LOGGING { print("📋 Using provided categoryId: \(catId)") }
        } else if let resdefId = processedData.resdefId, let resdefIdInt = Int(resdefId) {
            // Try to get category from resource definition like Python version
            // Level 3: Debug logging - category ID fetching
            if DEBUG_LOGGING { print("🔍 No categoryId provided, fetching from resdef API...") }
            do {
                let resourceDef = try await getResourceDefinitionAPI(id: resdefIdInt)
                categoryId = resourceDef.categoryId
                if DEBUG_LOGGING { print("✅ Got categoryId from resdef API: \(categoryId)") }
            } catch {
                // Level 3: Debug logging - category ID fetch failure
                if DEBUG_LOGGING { print("❌ Failed to get categoryId from resdef API: \(error)") }
                throw ResourceManagerError.categoryIdError("ResdefID \(resdefId) is invalid or not found")
            }
        } else {
            // Level 3: Debug logging - category ID validation error
            if DEBUG_LOGGING { print("❌ No categoryId and no valid resdefId") }
            throw ResourceManagerError.categoryIdError("Category ID is required when ResdefID is not provided")
        }

        requestBody["categoryId"] = categoryId

        // Level 3: Debug logging - request body
        if DEBUG_LOGGING { print("📤 Request body: \(requestBody)") }

        // Make API call
        let resourceId = try await performCreateRequest(body: requestBody)

        // If we have a resdefId, attach the resource to the resource definition
        if let resdefId = processedData.resdefId, let resdefIdInt = Int(resdefId) {
            try await attachResourceToResdef(resourceId: resourceId, resdefId: resdefIdInt)
        }

        return resourceId
    }

    private func updateResourceAPI(
        resourceId: Int,
        title: String?,
        priority: Int,
        quantity: Int,
        location: String
    ) async throws {
        // Level 3: Debug logging - update start
        if DEBUG_LOGGING { print("🔄 Updating resource \(resourceId)...") }

        let url = URL(string: "\(apiBaseURL)/tests/resources/\(resourceId)")!
        var request = URLRequest(url: url)
        request.httpMethod = "PUT"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("application/json", forHTTPHeaderField: "Accept")
        request.setValue(apiVersion, forHTTPHeaderField: "X-API-Version")

        if let token = authToken {
            request.setValue(token, forHTTPHeaderField: "Radar-Authentication")
        }

        var requestBody: [String: Any] = [
            "priority": priority,
            "specificLocation": location
        ]

        if let title = title {
            requestBody["title"] = title
        }

        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)

        // Level 3: Debug logging - request details
        if DEBUG_LOGGING { print("📤 Update request URL: \(url)") }
        if DEBUG_LOGGING { print("📤 Update request headers: \(request.allHTTPHeaderFields ?? [:])") }
        if DEBUG_LOGGING { print("📤 Update request body: \(requestBody)") }

        // Use the same session with authentication delegate as in authenticate()
        let delegate = AuthenticationDelegate()
        let session = URLSession(configuration: .default, delegate: delegate, delegateQueue: nil)

        let (_, response) = try await session.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw ResourceManagerError.networkError("Invalid response type")
        }

        // Level 3: Debug logging - response status
        if DEBUG_LOGGING { print("📥 Update response status: \(httpResponse.statusCode)") }

        // Accept 200, 204, and 205 as success for updates
        guard [200, 204, 205].contains(httpResponse.statusCode) else {
            // Special handling for 401 - Kerberos authentication issue
            if httpResponse.statusCode == 401 {
                throw ResourceManagerError.kerberosAuthenticationError("Kerberos authentication failed during resource update. Please run 'kinit <EMAIL>' to obtain a valid Kerberos ticket.")
            } else {
                throw ResourceManagerError.serverError("Update failed with status \(httpResponse.statusCode)")
            }
        }

        // Level 3: Debug logging - update success
        if DEBUG_LOGGING { print("✅ Resource \(resourceId) updated successfully") }
    }

    private func parseCSV(_ content: String) -> [[String: String]] {
        let lines = content.components(separatedBy: .newlines).filter { !$0.isEmpty }

        guard lines.count > 1 else {
            return []
        }

        let header = lines[0].components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespaces) }
        var results: [[String: String]] = []

        for line in lines.dropFirst() {
            let values = line.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespaces) }

            guard values.count == header.count else {
                continue
            }

            let rowData = Dictionary(uniqueKeysWithValues: zip(header, values))
            results.append(rowData)
        }

        return results
    }

    // MARK: - Title Processing Methods

    private func cleanTitle(_ title: String) -> (cleanedTitle: String, speedResdef: String) {
        // Extract the main title before any parentheses or speed indicators
        let components = title.components(separatedBy: " - ")
        if components.count > 1 {
            let mainTitle = components[0]
            let speedResdef = components.dropFirst().joined(separator: " - ")
            return (mainTitle, speedResdef)
        }
        return (title, "")
    }

    private func extractSpeedFromTitle(_ speedResdef: String) -> String {
        // Extract speed from patterns like "USB-C/FS" or "USB3_5"
        if speedResdef.contains("USB-C/FS") {
            return "FS"
        } else if speedResdef.contains("USB3_5") {
            return "USB3_5"
        } else if speedResdef.contains("/FS") {
            return "FS"
        }
        return "USB3_5" // Default speed
    }

    private func extractResdefId(from text: String) -> String? {
        // Extract resdef ID from patterns like "(resdef: 12345)" or just "12345"
        // First try the full pattern
        let fullPattern = "\\(resdef: (\\d+)\\)"
        if let regex = try? NSRegularExpression(pattern: fullPattern),
           let match = regex.firstMatch(in: text, range: NSRange(text.startIndex..., in: text)),
           let range = Range(match.range(at: 1), in: text) {
            return String(text[range])
        }

        // Then try to find any 5-digit number (like Swift version)
        let digitPattern = "\\d{5}"
        if let regex = try? NSRegularExpression(pattern: digitPattern),
           let match = regex.firstMatch(in: text, range: NSRange(text.startIndex..., in: text)),
           let range = Range(match.range, in: text) {
            return String(text[range])
        }

        return nil
    }

    private func createSampleResources() -> [ProcessedResourceWithExtras] {
        return [
            ProcessedResourceWithExtras(
                resourceId: "257840",
                resourceLink: "rdar://res/257840",
                title: "(Store MW673LL/A;MQTP3LL/A;MQTP3PA/A;MQTP3CH/A) Beats Studio Pro Wireless Headphones(A2924)(B453)(2023) - USB-C/FS (resdef: 25412)",
                priority: 2,
                resdefId: "25412",
                location: "Aruba A6",
                found: true,
                labelTitle: "Beats Studio Pro Wireless Headphones(A2924)(B453)(2023)",
                speed: "FS"
            )
        ]
    }

    private func generatePrintContent(from resources: [ProcessedResourceWithExtras]) -> String {
        // CSV Header matching the reference file format
        var content = "Title*,Label Title,Qty,speed,resdef*,Pri,resourceID*,res_link,resdef_link,location,Priority\n"

        // Generate CSV rows for each resource
        for resource in resources {
            let title = resource.title
            let labelTitle = resource.labelTitle
            let qty = "" // Empty as in Python reference
            let speed = resource.speed
            let resdefId = resource.resdefId ?? ""
            let priority = String(resource.priority)
            let resourceId = resource.resourceId
            let resourceLink = resource.resourceLink
            let resdefLink = !resdefId.isEmpty ? "rdar://resdef/\(resdefId)" : ""
            let location = resource.location ?? ""
            let priorityText = resource.priority == 5 ? "N/A" : String(resource.priority - 1) // Convert priority as in Python

            let row = "\"\(title)\",\"\(labelTitle)\",\(qty),\(speed),\(resdefId),\(priority),\(resourceId),\(resourceLink),\(resdefLink),\(location),\(priorityText)\n"
            content += row
        }

        return content
    }

    private func generatePrintCSVContent() -> String {
        let header = "Title*,Label Title,Qty,speed,resdef*,Pri,resourceID*,res_link,resdef_link,location,Priority"

        // For now, return a sample row. In a real implementation, this would fetch actual data
        let sampleRow = "Sample Resource,Sample Label,1,USB3_5,12345,3,67890,rdar://res/67890,rdar://resdef/12345,Aruba,3"

        return "\(header)\n\(sampleRow)\n"
    }
}

// MARK: - Resource Data Models

/// Resource data from API
struct ResourceData {
    let title: String
    let priority: Int
    let location: String
    let resdefId: String?
}

/// Processed resource data for creation
struct ProcessedResourceData {
    let title: String
    let resdefId: String?
    let priority: Int
    let location: String
    let categoryId: String?
}

/// Resource definition data from API
struct ResourceDefinitionData {
    let title: String
    let categoryId: Int
}

struct ResourceDefinitionFieldsData {
    let title: String
    let keywords: [String]
}

// MARK: - Processed Resource
/// Basic processed resource structure
struct ProcessedResource {
    let resourceId: String
    let resourceLink: String
    let title: String
    let priority: Int
    let resdefId: String?
    let location: String?
    let found: Bool
}

// MARK: - Extended Processed Resource
/// Extended ProcessedResource with additional fields for CSV generation
struct ProcessedResourceWithExtras {
    let resourceId: String
    let resourceLink: String
    let title: String
    let priority: Int
    let resdefId: String?
    let location: String?
    let found: Bool
    let labelTitle: String
    let speed: String

    init(
        resourceId: String,
        resourceLink: String,
        title: String,
        priority: Int,
        resdefId: String?,
        location: String?,
        found: Bool,
        labelTitle: String,
        speed: String
    ) {
        self.resourceId = resourceId
        self.resourceLink = resourceLink
        self.title = title
        self.priority = priority
        self.resdefId = resdefId
        self.location = location
        self.found = found
        self.labelTitle = labelTitle
        self.speed = speed
    }
}

// MARK: - Error Types
enum ResourceManagerError: Error, LocalizedError {
    case networkError(String)
    case authenticationError(String)
    case kerberosAuthenticationError(String)
    case serverError(String)
    case invalidCSV(String)
    case validationError(String)
    case categoryIdError(String)

    var errorDescription: String? {
        switch self {
        case .networkError(let message):
            return "Network Error: \(message)"
        case .authenticationError(let message):
            return "Authentication Error: \(message)"
        case .kerberosAuthenticationError(let message):
            return "Kerberos Authentication Error: \(message)"
        case .serverError(let message):
            return "Server Error: \(message)"
        case .invalidCSV(let message):
            return "CSV Error: \(message)"
        case .validationError(let message):
            return "Validation Error: \(message)"
        case .categoryIdError(let message):
            return "Category ID Error: \(message)"
        }
    }
}
