import Cocoa
import Combine



class ViewController: NSViewController {

    // MARK: - Outlets (Optional since they may not be connected in Storyboard yet)
    @IBOutlet weak var titleTextField: NSTextField?
    @IBOutlet weak var resdefIdTextField: NSTextField?

    @IBOutlet weak var quantityTextField: NSTextField?
    @IBOutlet weak var locationTextField: NSTextField?
    @IBOutlet weak var categoryIdTextField: NSTextField?
    @IBOutlet weak var createButton: NSButton?
    @IBOutlet weak var csvButton: NSButton?

    @IBOutlet weak var logTextView: NSTextView?
    @IBOutlet weak var tabView: NSTabView?

    // Update tab outlets
    @IBOutlet weak var updateCsvButton: NSButton?
    @IBOutlet weak var downloadTemplateButton: NSButton?
    @IBOutlet weak var printResourcesButton: NSButton?

    @IBOutlet weak var updateLogTextView: NSTextView?

    // Single resource update outlets
    @IBOutlet weak var updateResourceIdTextField: NSTextField?
    @IBOutlet weak var updateTitleTextField: NSTextField?
    @IBOutlet weak var updatePriorityPopUpButton: NSPopUpButton?
    @IBOutlet weak var updateLocationTextField: NSTextField?
    @IBOutlet weak var updateSingleButton: NSButton?

    // Note: Some outlets may not be connected yet - they will be connected in Interface Builder

    // MARK: - Properties
    private var resourceManager: ResourceManagerCore!
    private var appService: ApplicationService!
    private var cancellables = Set<AnyCancellable>()

    // Store the latest resource data for print functionality
    private var latestResourceData: [ProcessedResourceWithExtras] = []

    // Keep reference to current preview window
    private var currentPreviewController: ResourcePreviewWindowController?
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        setupUI()
        setupResourceManager()
        logMessage("✅ Resource Manager loaded successfully")

        // Log to console window
        if let appDelegate = NSApp.delegate as? AppDelegate {
            appDelegate.addToConsoleLog("ℹ️ INFO: Resource Manager application started")
        }
    }
    
    private func setupUI() {


        // Setup default values (if connected)
        locationTextField?.stringValue = "Aruba"
        quantityTextField?.stringValue = "1"

        // Setup text field placeholders and properties (if connected)
        setupTextField(titleTextField, placeholder: "Enter resource title")
        setupTextField(resdefIdTextField, placeholder: "Enter resdef ID")
        setupTextField(categoryIdTextField, placeholder: "Optional")
        setupTextField(quantityTextField, placeholder: nil)
        setupTextField(locationTextField, placeholder: nil)

        // Setup log text view (if connected)
        setupLogTextView(logTextView)

        // Setup update log text view (if connected)
        setupLogTextView(updateLogTextView)

        // Setup update priority popup (if connected)
        if let updatePriorityPopUp = updatePriorityPopUpButton {
            updatePriorityPopUp.removeAllItems()
            updatePriorityPopUp.addItems(withTitles: ["N/A", "0", "1", "2", "3", "4", "5"])
            updatePriorityPopUp.selectItem(at: 0) // Default to N/A (empty)
        }

        // Setup update default values (if connected)
        updateLocationTextField?.stringValue = "Aruba"

        // Setup update text field placeholders and properties (if connected)
        setupTextField(updateResourceIdTextField, placeholder: "Enter resource ID (required)")
        setupTextField(updateTitleTextField, placeholder: "Enter new title (optional)")
        setupTextField(updateLocationTextField, placeholder: "Enter location (optional)")

    }

    private func setupTextField(_ textField: NSTextField?, placeholder: String?) {
        guard let textField = textField else { return }

        if let placeholder = placeholder {
            textField.placeholderString = placeholder
        }

        textField.isEditable = true
        textField.isSelectable = true
        textField.allowsEditingTextAttributes = true
        textField.importsGraphics = false

        // Enable standard text editing menu
        textField.usesSingleLineMode = true
        textField.lineBreakMode = .byTruncatingTail

        // Configure key view behavior
        textField.nextKeyView = nil
    }

    private func setupLogTextView(_ textView: NSTextView?) {
        guard let textView = textView else { return }

        textView.isEditable = false
        textView.isSelectable = true
        textView.allowsUndo = false
        textView.font = NSFont.monospacedSystemFont(ofSize: 11, weight: .regular)
        textView.isAutomaticLinkDetectionEnabled = true
        textView.checkTextInDocument(nil)

        // Enable standard text operations like copy
        textView.isRichText = true
        textView.importsGraphics = false
        textView.allowsImageEditing = false
        textView.allowsDocumentBackgroundColorChange = false

        // Configure text view for copy operations
        // Note: canBecomeKeyView is read-only, controlled by other properties
    }

    private func setupResourceManager() {
        resourceManager = ResourceManagerCore()
        appService = ApplicationService()

        // Subscribe to ApplicationService output for Create Resource operations
        appService.outputPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] output in
                // Only log to Create Resource panel for non-update operations
                if !output.contains("update") && !output.contains("Update") {
                    let trimmedOutput = output.trimmingCharacters(in: .whitespacesAndNewlines)

                    // Check if the message contains a resource link
                    if let linkRange = trimmedOutput.range(of: "rdar://res/\\d+", options: .regularExpression) {
                        let link = String(trimmedOutput[linkRange])
                        self?.logMessageWithLink(trimmedOutput, link: link)
                    } else {
                        self?.logMessage(trimmedOutput)
                    }
                }
            }
            .store(in: &cancellables)

        // Subscribe to ApplicationService output for Resource Update operations
        appService.outputPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] output in
                // Only log to Resource Update panel for update operations, Keywords API, and detailed resource info
                if output.contains("update") || output.contains("Update") || output.contains("🔄") || output.contains("🔍") || output.contains("Keywords API") || output.contains("✅ Using priority") || output.contains("⚠️ No priority") || output.contains("⚠️ Failed to extract") || output.hasPrefix("      ") {
                    let trimmedOutput = output.trimmingCharacters(in: .whitespacesAndNewlines)

                    // Check if the message contains a resource link
                    if let linkRange = trimmedOutput.range(of: "rdar://res/\\d+", options: .regularExpression) {
                        let link = String(trimmedOutput[linkRange])
                        self?.logUpdateMessageWithLink(trimmedOutput, link: link)
                    } else {
                        self?.logUpdateMessage(trimmedOutput)
                    }
                }
            }
            .store(in: &cancellables)


    }
    
    // MARK: - Actions
    
    @IBAction func createResourceClicked(_ sender: NSButton) {


        let title = titleTextField?.stringValue.isEmpty == false ? titleTextField?.stringValue : nil
        let resdefId = resdefIdTextField?.stringValue.isEmpty == false ? resdefIdTextField?.stringValue : nil
        // Priority will be automatically determined from Keywords API
        let quantity = quantityTextField?.stringValue.isEmpty == false ? quantityTextField?.stringValue : "1"
        let location = locationTextField?.stringValue.isEmpty == false ? locationTextField?.stringValue : ResourceManagerConfig.shared.specificLocation
        let categoryId = categoryIdTextField?.stringValue.isEmpty == false ? categoryIdTextField?.stringValue : nil

        // Validate input - Either title or resdefId is required
        guard (title != nil && !title!.isEmpty) || (resdefId != nil && !resdefId!.isEmpty) else {
            showAlert(title: "Input Error", message: "Either Title or ResdefID is required")
            return
        }


        
        // Disable button during operation
        createButton?.isEnabled = false

        // Log to console window
        if let appDelegate = NSApp.delegate as? AppDelegate {
            appDelegate.addToConsoleLog("ℹ️ INFO: Starting single resource creation - Title: \(title ?? "N/A"), ResdefID: \(resdefId ?? "N/A")")
        }

        Task<Void, Never> {
            let response = await appService.createSingleResource(
                title: title,
                resdefId: resdefId,
                priority: "5", // Default priority, will be overridden by Keywords API
                quantity: quantity ?? "1",
                location: location ?? ResourceManagerConfig.shared.specificLocation,
                categoryId: categoryId
            )

            await MainActor.run {
                if response.isSuccess, let resources = response.data, !resources.isEmpty {
                    logMessage("✅ Successfully created \(resources.count) resource(s)!")
                    for (index, resource) in resources.enumerated() {
                        logMessage("🔗 Resource \(index + 1) ID: \(resource.resourceId)")
                        if let link = resource.resourceLink {
                            logMessageWithLink("🔗 Resource \(index + 1) Link: \(link)", link: link)
                        }
                    }

                    // Show preview window with created resources
                    showResourcePreviewFromProcessedResources(resources: resources)
                } else if response.errorCode == "CATEGORY_ID_ERROR" {
                    showCategoryIdDialog(
                        message: response.message ?? "ResdefID is invalid or not found.\nPlease enter a Category ID:",
                        title: title,
                        resdefId: resdefId,
                        priority: "5", // Default priority, will be overridden by Keywords API
                        quantity: quantity,
                        location: location
                    )
                } else {
                    logMessage("❌ Failed to create resource: \(response.message ?? "Unknown error")")
                    showAlert(title: "Creation Error", message: response.message ?? "Unknown error")
                }
                createButton?.isEnabled = true
            }
        }
    }
    
    @IBAction func csvButtonClicked(_ sender: NSButton) {


        let openPanel = NSOpenPanel()
        openPanel.allowedContentTypes = [.commaSeparatedText]
        openPanel.allowsMultipleSelection = false
        openPanel.canChooseDirectories = false
        openPanel.canChooseFiles = true

        openPanel.begin { response in
            if response == .OK, let url = openPanel.url {

                self.createResourcesFromCSV(url: url)
            }
        }
    }
    


    @IBAction func updateCsvButtonClicked(_ sender: NSButton) {


        let openPanel = NSOpenPanel()
        openPanel.allowedContentTypes = [.commaSeparatedText]
        openPanel.allowsMultipleSelection = false
        openPanel.canChooseDirectories = false
        openPanel.canChooseFiles = true

        openPanel.begin { response in
            if response == .OK, let url = openPanel.url {

                self.updateResourcesFromCSV(url: url)
            }
        }
    }

    @IBAction func downloadTemplateButtonClicked(_ sender: NSButton) {


        let savePanel = NSSavePanel()
        savePanel.allowedContentTypes = [.commaSeparatedText]
        savePanel.nameFieldStringValue = "ResourceUpdateTemplate.csv"

        savePanel.begin { response in
            if response == .OK, let url = savePanel.url {

                self.downloadTemplate(to: url)
            }
        }
    }

    @IBAction func updateSingleResourceClicked(_ sender: NSButton) {
        let resourceIdString = updateResourceIdTextField?.stringValue.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        let title = updateTitleTextField?.stringValue.isEmpty == false ? updateTitleTextField?.stringValue : nil
        let priorityTitle = updatePriorityPopUpButton?.titleOfSelectedItem ?? "N/A"
        let priority = (priorityTitle == "N/A") ? "" : priorityTitle
        let location = updateLocationTextField?.stringValue.isEmpty == false ? updateLocationTextField?.stringValue : nil

        // Validate input - ResourceID is required
        guard !resourceIdString.isEmpty else {
            showAlert(title: "Input Error", message: "ResourceID is required for updates")
            return
        }

        // Convert resourceID to integer
        guard let resourceId = Int(resourceIdString.replacingOccurrences(of: "rdar://res/", with: "")) else {
            showAlert(title: "Input Error", message: "Invalid ResourceID format. Please enter a valid resource ID number.")
            return
        }

        // Disable button during operation
        updateSingleButton?.isEnabled = false

        // Log to console window
        if let appDelegate = NSApp.delegate as? AppDelegate {
            appDelegate.addToConsoleLog("ℹ️ INFO: Starting single resource update - ResourceID: \(resourceId), Title: \(title ?? "N/A")")
        }

        Task<Void, Never> {
            // Convert priority - if empty string, pass nil; otherwise convert to Int
            let priorityValue: Int? = priority.isEmpty ? nil : (Int(priority) ?? 5)

            let response = await appService.updateSingleResource(
                resourceId: resourceId,
                title: title,
                priority: priorityValue,
                location: location ?? "Aruba"
            )

            await MainActor.run {
                if response.isSuccess, let resources = response.data {
                    logUpdateMessage("✅ Resource updated successfully!")
                    for resource in resources {
                        logUpdateMessage("🔗 Resource ID: \(resource.resourceId)")
                        if let link = resource.resourceLink {
                            logUpdateMessageWithLink("🔗 Resource Link: \(link)", link: link)
                        }
                    }

                    // Show preview window with updated resources
                    showResourcePreview(results: resources)
                } else {
                    logUpdateMessage("❌ Failed to update resource: \(response.message ?? "Unknown error")")

                    // Check if this is a Kerberos authentication error
                    if let errorMessage = response.message, errorMessage.contains("Kerberos authentication failed") {
                        showKerberosAlert(title: "Update Error", message: errorMessage)
                    } else {
                        showAlert(title: "Update Error", message: response.message ?? "Unknown error")
                    }
                }
                updateSingleButton?.isEnabled = true
            }
        }
    }

    @IBAction func printResourcesClicked(_ sender: NSButton) {
        // Check if there's an open preview window and get latest data from it
        if let previewController = currentPreviewController {
            if let window = previewController.window {
                if window.isVisible {
                    // Force the preview window to check for data changes and save them
                    previewController.checkForDataChanges()
                    previewController.saveChangesToResources()
                } else {
                    // Still try to get data from the closed window
                    previewController.checkForDataChanges()
                    previewController.saveChangesToResources()
                }
            }
        }

        guard !latestResourceData.isEmpty else {
            showAlert(title: "No Data", message: "No resource data available to print. Please create or update resources first.")
            return
        }



        // Disable button during operation
        printResourcesButton?.isEnabled = false

        Task {
            do {
                let csvContent = generateCSVContent(from: latestResourceData)
                let outputPath = try await saveCSVFile(content: csvContent)

                await MainActor.run {
                    logMessage("✅ Print file generated: \(outputPath)")

                    // Show success message
                    let alert = NSAlert()
                    alert.messageText = "Print File Generated"
                    alert.informativeText = "File saved to: \(outputPath)"
                    alert.alertStyle = .informational
                    alert.addButton(withTitle: "Open in Finder")
                    alert.addButton(withTitle: "OK")

                    let response = alert.runModal()
                    if response == .alertFirstButtonReturn {
                        NSWorkspace.shared.selectFile(outputPath, inFileViewerRootedAtPath: "")
                    }

                    printResourcesButton?.isEnabled = true
                }
            } catch {
                await MainActor.run {
                    logMessage("❌ Failed to generate print file: \(error.localizedDescription)")
                    showAlert(title: "Print Error", message: error.localizedDescription)
                    printResourcesButton?.isEnabled = true
                }
            }
        }
    }


    
    // MARK: - Helper Methods
    
    private func createResourcesFromCSV(url: URL) {
        csvButton?.isEnabled = false
        
        Task {
            do {
                let results = try await resourceManager.createResourcesFromCSV(fileURL: url)
                
                await MainActor.run {
                    logMessage("✅ Created \(results.count) resources from CSV")
                    for result in results {
                        logMessage("   Resource ID: \(result.resourceId)")
                        if let link = result.resourceLink {
                            logMessageWithLink("   Resource Link: \(link)", link: link)
                        }
                    }

                    // Show preview window with created resources
                    showResourcePreview(results: results)
                    csvButton?.isEnabled = true
                }
            } catch {
                await MainActor.run {
                    logMessage("❌ Failed to create resources from CSV: \(error.localizedDescription)")

                    // Check if this is a Kerberos authentication error
                    if let kerberosError = error as? ResourceManagerError,
                       case .kerberosAuthenticationError(let message) = kerberosError {
                        showKerberosAlert(title: "CSV Error", message: message)
                    } else {
                        showAlert(title: "CSV Error", message: error.localizedDescription)
                    }

                    csvButton?.isEnabled = true
                }
            }
        }
    }

    private func updateResourcesFromCSV(url: URL) {
        updateCsvButton?.isEnabled = false


        Task {
            do {
                await MainActor.run {
                    logUpdateMessage("🚀 Starting resource update process...")
                    logUpdateMessage("📁 Selected file: \(url.lastPathComponent)")
                    logUpdateMessage("📍 Full path: \(url.path)")
                }

                // Check if file exists
                guard FileManager.default.fileExists(atPath: url.path) else {
                    await MainActor.run {
                        logUpdateMessage("❌ File not found at path: \(url.path)")
                        updateCsvButton?.isEnabled = true
                    }
                    return
                }

                await MainActor.run {
                    logUpdateMessage("✅ File exists, validating CSV format...")
                }

                // Validate CSV format before processing
                do {
                    let csvContent = try String(contentsOf: url)
                    let lines = csvContent.components(separatedBy: .newlines).filter { !$0.isEmpty }

                    await MainActor.run {
                        logUpdateMessage("📊 Found \(lines.count) lines in CSV (including header)")

                        if lines.count <= 1 {
                            logUpdateMessage("⚠️ CSV file must contain at least a header and one data row")
                        } else {
                            logUpdateMessage("✅ CSV format validation passed")
                            logUpdateMessage("🔄 Starting API calls to update \(lines.count - 1) resource(s)...")
                        }
                    }
                } catch {
                    await MainActor.run {
                        logUpdateMessage("❌ Failed to read CSV file: \(error.localizedDescription)")
                        updateCsvButton?.isEnabled = true
                    }
                    return
                }

                // Call ResourceManagerCore directly for better control
                let results = try await resourceManager.updateResourcesFromCSV(fileURL: url)

                await MainActor.run {
                    if !results.isEmpty {
                        logUpdateMessage("✅ Batch update completed - \(results.count) resource(s) processed")

                        // Generate combined resource link with & separator (correct format)
                        let resourceIds = results.map { String($0.resourceId) }
                        let combinedLink = "rdar://res/\(resourceIds.joined(separator: "&"))"

                        logUpdateMessageWithLink("🔗 Combined Resource Link: \(combinedLink)", link: combinedLink)

                        // Copy combined link to clipboard
                        let pasteboard = NSPasteboard.general
                        pasteboard.clearContents()
                        pasteboard.setString(combinedLink, forType: .string)
                        logUpdateMessage("📋 Combined link copied to clipboard")

                        // Show preview window with updated resources
                        showResourcePreview(results: results)

                    } else {
                        logUpdateMessage("⚠️ No resources were updated")
                    }
                    updateCsvButton?.isEnabled = true
                }
            } catch {
                await MainActor.run {
                    logUpdateMessage("❌ Failed to update resources: \(error.localizedDescription)")

                    // Check if this is a Kerberos authentication error
                    if let kerberosError = error as? ResourceManagerError,
                       case .kerberosAuthenticationError(let message) = kerberosError {
                        showKerberosAlert(title: "Update Error", message: message)
                    } else {
                        showAlert(title: "Update Error", message: error.localizedDescription)
                    }

                    updateCsvButton?.isEnabled = true
                }
            }
        }
    }

    private func downloadTemplate(to url: URL) {
        Task {
            let response = await appService.downloadUpdateTemplate(to: url)

            await MainActor.run {
                if response.isSuccess {
                    logMessage("✅ Template downloaded successfully")

                    // Open the file in Finder
                    NSWorkspace.shared.selectFile(url.path, inFileViewerRootedAtPath: "")
                } else {
                    logMessage("❌ Failed to download template: \(response.message ?? "Unknown error")")
                    showAlert(title: "Download Error", message: response.message ?? "Unknown error")
                }
            }
        }
    }

    private func logMessage(_ message: String) {
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .medium)
        let logEntry = "[\(timestamp)] \(message)\n"

        DispatchQueue.main.async {
            self.logTextView?.textStorage?.append(NSAttributedString(string: logEntry))
            self.logTextView?.scrollToEndOfDocument(nil)
        }
    }

    private func logMessageWithLink(_ message: String, link: String? = nil) {
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .medium)
        let timestampText = "[\(timestamp)] "

        DispatchQueue.main.async {
            let attributedString = NSMutableAttributedString()

            // Add timestamp
            attributedString.append(NSAttributedString(string: timestampText))

            if let link = link, message.contains(link) {
                // Split message around the link
                let parts = message.components(separatedBy: link)

                // Add text before link
                if !parts.isEmpty {
                    attributedString.append(NSAttributedString(string: parts[0]))
                }

                // Add clickable link
                let linkAttributes: [NSAttributedString.Key: Any] = [
                    .link: URL(string: link) ?? link,
                    .foregroundColor: NSColor.systemBlue,
                    .underlineStyle: NSUnderlineStyle.single.rawValue
                ]
                attributedString.append(NSAttributedString(string: link, attributes: linkAttributes))

                // Add text after link
                if parts.count > 1 {
                    attributedString.append(NSAttributedString(string: parts[1]))
                }
            } else {
                // No link, just add the message
                attributedString.append(NSAttributedString(string: message))
            }

            attributedString.append(NSAttributedString(string: "\n"))

            self.logTextView?.textStorage?.append(attributedString)
            self.logTextView?.scrollToEndOfDocument(nil)
        }
    }

    private func logUpdateMessage(_ message: String) {
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .medium)
        let logEntry = "[\(timestamp)] \(message)\n"

        DispatchQueue.main.async {
            self.updateLogTextView?.textStorage?.append(NSAttributedString(string: logEntry))
            self.updateLogTextView?.scrollToEndOfDocument(nil)
        }
    }

    private func logUpdateMessageWithLink(_ message: String, link: String? = nil) {
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .medium)
        let timestampText = "[\(timestamp)] "

        DispatchQueue.main.async {
            let attributedString = NSMutableAttributedString()

            // Add timestamp
            attributedString.append(NSAttributedString(string: timestampText))

            if let link = link, message.contains(link) {
                // Split message around the link
                let parts = message.components(separatedBy: link)

                // Add text before link
                if !parts.isEmpty {
                    attributedString.append(NSAttributedString(string: parts[0]))
                }

                // Add clickable link
                let linkAttributes: [NSAttributedString.Key: Any] = [
                    .link: URL(string: link) ?? link,
                    .foregroundColor: NSColor.systemBlue,
                    .underlineStyle: NSUnderlineStyle.single.rawValue
                ]
                attributedString.append(NSAttributedString(string: link, attributes: linkAttributes))

                // Add text after link
                if parts.count > 1 {
                    attributedString.append(NSAttributedString(string: parts[1]))
                }
            } else {
                // No link, just add the message
                attributedString.append(NSAttributedString(string: message))
            }

            attributedString.append(NSAttributedString(string: "\n"))

            self.updateLogTextView?.textStorage?.append(attributedString)
            self.updateLogTextView?.scrollToEndOfDocument(nil)
        }
    }
    
    private func showAlert(title: String, message: String) {
        let alert = NSAlert()
        alert.messageText = title
        alert.informativeText = message
        alert.alertStyle = .warning
        alert.addButton(withTitle: "OK")
        alert.runModal()
    }

    private func showKerberosAlert(title: String, message: String) {
        let alert = NSAlert()
        alert.messageText = title
        alert.informativeText = message
        alert.alertStyle = .warning
        alert.addButton(withTitle: "Copy kinit Command")
        alert.addButton(withTitle: "OK")

        let response = alert.runModal()

        if response == .alertFirstButtonReturn {
            // Copy the kinit command to clipboard
            let kinitCommand = "kinit <EMAIL>"
            let pasteboard = NSPasteboard.general
            pasteboard.clearContents()
            pasteboard.setString(kinitCommand, forType: .string)

            // Show confirmation
            let confirmAlert = NSAlert()
            confirmAlert.messageText = "Command Copied"
            confirmAlert.informativeText = "The kinit command has been copied to your clipboard. Please run it in Terminal to obtain a Kerberos ticket, then try again."
            confirmAlert.alertStyle = .informational
            confirmAlert.addButton(withTitle: "OK")
            confirmAlert.runModal()
        }
    }

    private func showResourcePreview(resources: [ProcessedResourceWithExtras]) {
        // Store the latest resource data
        latestResourceData = resources

        // Resources are already in the correct format for preview
        let previewController = ResourcePreviewWindowController(resources: resources)

        // Keep reference to current preview window
        currentPreviewController = previewController

        // Set callback to update stored data when user edits in preview
        previewController.onDataChanged = { [weak self] updatedResources in
            self?.latestResourceData = updatedResources

        }

        // Set callback to clear reference when window closes
        previewController.onWindowClosing = { [weak self] in
            self?.currentPreviewController = nil
        }

        previewController.showWindow(self)
    }

    private func showResourcePreview(results: [ResourceCreationResult]) {
        // Convert ResourceCreationResult to ProcessedResourceWithExtras for preview
        let resourcesWithExtras = results.map { result in
            ProcessedResourceWithExtras(
                resourceId: String(result.resourceId),
                resourceLink: result.resourceLink ?? "rdar://res/\(result.resourceId)",
                title: result.title ?? "Unknown Title",
                priority: result.priority ?? 5,
                resdefId: nil, // Not available in ResourceCreationResult
                location: result.location,
                found: true, // Assume found since they were successfully created
                labelTitle: extractLabelTitle(from: result.title ?? "Unknown Title"),
                speed: extractSpeed(from: result.title ?? "Unknown Title")
            )
        }

        // Store the latest resource data
        latestResourceData = resourcesWithExtras

        let previewController = ResourcePreviewWindowController(resources: resourcesWithExtras)

        // Keep reference to current preview window
        currentPreviewController = previewController

        // Set callback to update stored data when user edits in preview
        previewController.onDataChanged = { [weak self] updatedResources in
            self?.latestResourceData = updatedResources

        }

        // Set callback to clear reference when window closes
        previewController.onWindowClosing = { [weak self] in
            self?.currentPreviewController = nil
        }

        previewController.showWindow(self)
    }

    private func showResourcePreviewFromProcessedResources(resources: [ResourceCreationResult]) {
        // Convert ResourceCreationResult to ProcessedResourceWithExtras for preview
        let resourcesWithExtras = resources.map { result in
            ProcessedResourceWithExtras(
                resourceId: String(result.resourceId),
                resourceLink: result.resourceLink ?? "rdar://res/\(result.resourceId)",
                title: result.title ?? "Unknown Title",
                priority: result.priority ?? 5,
                resdefId: nil, // Not available in ResourceCreationResult
                location: result.location,
                found: true, // Assume found since they were successfully created
                labelTitle: extractLabelTitle(from: result.title ?? "Unknown Title"),
                speed: extractSpeed(from: result.title ?? "Unknown Title")
            )
        }

        // Store the latest resource data
        latestResourceData = resourcesWithExtras

        let previewController = ResourcePreviewWindowController(resources: resourcesWithExtras)

        // Keep reference to current preview window
        currentPreviewController = previewController

        // Set callback to update stored data when user edits in preview
        previewController.onDataChanged = { [weak self] updatedResources in
            self?.latestResourceData = updatedResources

        }

        // Set callback to clear reference when window closes
        previewController.onWindowClosing = { [weak self] in
            self?.currentPreviewController = nil
        }

        previewController.showWindow(self)
    }

    private func extractLabelTitle(from title: String) -> String {
        // Extract label title from full title (remove store codes and resdef info)
        // Example: "(Store MW673LL/A;MQTP3LL/A) Beats Studio Pro Wireless Headphones(A2924)(B453)(2023) - USB-C/FS (resdef: 25412)"
        // Should return: "Beats Studio Pro Wireless Headphones(A2924)(B453)(2023)"

        var cleanTitle = title

        // Remove store codes pattern: "(Store ...)"
        if let storeRange = cleanTitle.range(of: #"\(Store [^)]+\)\s*"#, options: .regularExpression) {
            cleanTitle.removeSubrange(storeRange)
        }

        // Remove resdef pattern: " (resdef: ...)"
        if let resdefRange = cleanTitle.range(of: #"\s*\(resdef:\s*[^)]+\)"#, options: .regularExpression) {
            cleanTitle.removeSubrange(resdefRange)
        }

        // Remove speed pattern: " - USB-C/FS" or similar
        if let speedRange = cleanTitle.range(of: #"\s*-\s*[^-]*/(FS|USB3_5|USB2)"#, options: .regularExpression) {
            cleanTitle.removeSubrange(speedRange)
        }

        return cleanTitle.trimmingCharacters(in: .whitespacesAndNewlines)
    }

    private func extractSpeed(from title: String) -> String {
        // Extract speed from title (FS, USB3_5, USB2, etc.)
        if let speedMatch = title.range(of: #"/(FS|USB3_5|USB2)"#, options: .regularExpression) {
            let speedPart = String(title[speedMatch])
            return String(speedPart.dropFirst()) // Remove the "/"
        }
        return "FS" // Default speed
    }

    private func showCategoryIdDialog(
        message: String,
        title: String?,
        resdefId: String?,
        priority: String?,
        quantity: String?,
        location: String?
    ) {
        let alert = NSAlert()
        alert.messageText = "Category ID Required"
        alert.informativeText = message
        alert.alertStyle = .informational
        alert.addButton(withTitle: "OK")
        alert.addButton(withTitle: "Cancel")

        let textField = NSTextField(frame: NSRect(x: 0, y: 0, width: 200, height: 24))
        textField.placeholderString = "Enter Category ID"
        alert.accessoryView = textField

        let response = alert.runModal()

        if response == .alertFirstButtonReturn {
            let categoryId = textField.stringValue
            if !categoryId.isEmpty {
                // Update the category ID field in UI
                categoryIdTextField?.stringValue = categoryId



                // Retry resource creation with the provided category ID
                Task<Void, Never> {
                    let response = await appService.createSingleResource(
                        title: title,
                        resdefId: resdefId,
                        priority: priority,
                        quantity: quantity ?? "1",
                        location: location ?? "Aruba",
                        categoryId: categoryId
                    )

                    await MainActor.run {
                        if response.isSuccess, let resources = response.data, !resources.isEmpty {
                            logMessage("✅ Successfully created \(resources.count) resource(s) with provided categoryId!")
                            for (index, resource) in resources.enumerated() {
                                logMessage("🔗 Resource \(index + 1) ID: \(resource.resourceId)")
                                if let link = resource.resourceLink {
                                    logMessageWithLink("🔗 Resource \(index + 1) Link: \(link)", link: link)
                                }
                            }

                            // Show preview window with created resources
                            showResourcePreviewFromProcessedResources(resources: resources)
                        } else {
                            logMessage("❌ Failed to create resource even with categoryId: \(response.message ?? "Unknown error")")
                            showAlert(title: "Creation Error", message: response.message ?? "Unknown error")
                        }
                    }
                }
            }
        }
    }

    // MARK: - CSV Generation for Print

    private func generateCSVContent(from resources: [ProcessedResourceWithExtras]) -> String {
        let columnHeaders = [
            "Title*", "Label Title", "Qty", "speed", "resdef*",
            "Pri", "resourceID*", "res_link", "resdef_link", "location", "Priority"
        ]

        var content = columnHeaders.joined(separator: ",") + "\n"

        for resource in resources {
            let title = resource.title
            let labelTitle = resource.labelTitle
            let qty = "" // Empty as in original implementation
            let speed = resource.speed
            let resdefId = resource.resdefId ?? ""
            let priority = String(resource.priority)
            let resourceId = resource.resourceId
            let resourceLink = resource.resourceLink
            let resdefLink = !resdefId.isEmpty ? "rdar://resdef/\(resdefId)" : ""
            let location = resource.location ?? ""
            let priorityText = resource.priority == 5 ? "N/A" : String(resource.priority - 1)

            let row = [
                title, labelTitle, qty, speed, resdefId,
                priority, resourceId, resourceLink, resdefLink, location, priorityText
            ]

            let escapedRow = row.map { value in
                // Escape quotes and wrap in quotes if necessary
                if value.contains(",") || value.contains("\"") || value.contains("\n") {
                    return "\"\(value.replacingOccurrences(of: "\"", with: "\"\""))\""
                }
                return value
            }
            content += escapedRow.joined(separator: ",") + "\n"
        }

        return content
    }

    private func saveCSVFile(content: String) async throws -> String {
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .short, timeStyle: .short)
        let filename = "ResourcePrint_\(timestamp.replacingOccurrences(of: "/", with: "-").replacingOccurrences(of: ":", with: "-")).csv"

        let downloadsURL = FileManager.default.urls(for: .downloadsDirectory, in: .userDomainMask).first!
        let outputURL = downloadsURL.appendingPathComponent(filename)

        try content.write(to: outputURL, atomically: true, encoding: .utf8)

        return outputURL.path
    }
}


