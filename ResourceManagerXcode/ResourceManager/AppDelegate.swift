import Cocoa

@main
class AppDelegate: NSObject, NSApplicationDelegate, NSWindowDelegate {

    private weak var consoleWindow: NSWindow?
    private weak var consoleTextView: NSTextView?
    private var consoleLogContent: NSMutableAttributedString = NSMutableAttributedString()
    private var isConsoleWindowClosing = false

    private weak var configWindow: NSWindow?
    private var isConfigWindowClosing = false

    func applicationDidFinishLaunching(_ aNotification: Notification) {
        // Insert code here to initialize your application
        print("🚀 Resource Manager started")

        // Setup application menu with Edit menu for copy/paste support
        setupApplicationMenu()

        // Ensure app comes to front
        NSApp.activate(ignoringOtherApps: true)
    }

    private func setupApplicationMenu() {
        let mainMenu = NSMenu()

        // App Menu
        let appMenuItem = NSMenuItem()
        let appMenu = NSMenu()
        appMenu.addItem(NSMenuItem(title: "Quit Resource Manager", action: #selector(NSApplication.terminate(_:)), keyEquivalent: "q"))
        appMenuItem.submenu = appMenu
        mainMenu.addItem(appMenuItem)

        // Edit Menu
        let editMenuItem = NSMenuItem(title: "Edit", action: nil, keyEquivalent: "")
        let editMenu = NSMenu(title: "Edit")

        editMenu.addItem(NSMenuItem(title: "Cut", action: #selector(NSText.cut(_:)), keyEquivalent: "x"))
        editMenu.addItem(NSMenuItem(title: "Copy", action: #selector(NSText.copy(_:)), keyEquivalent: "c"))
        editMenu.addItem(NSMenuItem(title: "Paste", action: #selector(NSText.paste(_:)), keyEquivalent: "v"))
        editMenu.addItem(NSMenuItem.separator())
        editMenu.addItem(NSMenuItem(title: "Select All", action: #selector(NSText.selectAll(_:)), keyEquivalent: "a"))

        editMenuItem.submenu = editMenu
        mainMenu.addItem(editMenuItem)

        // View Menu
        let viewMenuItem = NSMenuItem(title: "View", action: nil, keyEquivalent: "")
        let viewMenu = NSMenu(title: "View")

        viewMenu.addItem(NSMenuItem(title: "Show Console Log", action: #selector(showConsoleLog), keyEquivalent: "l"))
        viewMenu.addItem(NSMenuItem(title: "Clear Console Log", action: #selector(clearConsoleLog), keyEquivalent: "k"))

        viewMenuItem.submenu = viewMenu
        mainMenu.addItem(viewMenuItem)

        // Configuration Menu
        let configMenuItem = NSMenuItem(title: "Configuration", action: nil, keyEquivalent: "")
        let configMenu = NSMenu(title: "Configuration")

        configMenu.addItem(NSMenuItem(title: "Default Values...", action: #selector(showConfigurationWindow), keyEquivalent: ","))

        configMenuItem.submenu = configMenu
        mainMenu.addItem(configMenuItem)

        NSApp.mainMenu = mainMenu
    }

    func applicationWillTerminate(_ aNotification: Notification) {
        // Insert code here to tear down your application
    }

    func applicationSupportsSecureRestorableState(_ app: NSApplication) -> Bool {
        return true
    }
    
    func applicationShouldTerminateAfterLastWindowClosed(_ sender: NSApplication) -> Bool {
        return true
    }

    // MARK: - Console Log Methods

    @objc private func showConsoleLog() {
        // Prevent creating window while another is closing
        if isConsoleWindowClosing {
            return
        }

        // Always create a new window if the current one is nil or not visible
        if consoleWindow == nil {
            createConsoleWindow()
        } else if consoleWindow?.isVisible == false {
            // Window exists but is not visible, show it
            consoleWindow?.makeKeyAndOrderFront(nil)
        } else {
            // Window is already visible, just bring it to front
            consoleWindow?.makeKeyAndOrderFront(nil)
        }

        NSApp.activate(ignoringOtherApps: true)
    }

    @objc private func clearConsoleLog() {
        consoleLogContent.mutableString.setString("")
        consoleTextView?.textStorage?.setAttributedString(consoleLogContent)
    }

    private func createConsoleWindow() {
        // Prevent creating window while another is closing
        if isConsoleWindowClosing {
            return
        }

        // Clean up any existing window first
        if let existingWindow = consoleWindow {
            existingWindow.delegate = nil
            existingWindow.orderOut(nil)
            consoleWindow = nil
            consoleTextView = nil
        }

        // Create window with strong reference
        let windowRect = NSRect(x: 100, y: 100, width: 800, height: 600)
        let newWindow = NSWindow(
            contentRect: windowRect,
            styleMask: [.titled, .closable, .resizable, .miniaturizable],
            backing: .buffered,
            defer: false
        )

        newWindow.title = "Resource Manager - Console Log"
        newWindow.center()
        newWindow.isReleasedWhenClosed = false  // Important: prevent auto-release

        // Set up window delegate to handle window closing
        newWindow.delegate = self

        // Store weak reference
        consoleWindow = newWindow

        // Create scroll view
        guard let contentView = newWindow.contentView else {
            print("Failed to get window content view")
            return
        }

        let scrollView = NSScrollView(frame: contentView.bounds)
        scrollView.autoresizingMask = [.width, .height]
        scrollView.hasVerticalScroller = true
        scrollView.hasHorizontalScroller = true
        scrollView.autohidesScrollers = false

        // Create text view with strong reference first
        let newTextView = NSTextView(frame: scrollView.bounds)
        newTextView.autoresizingMask = [.width, .height]
        newTextView.isEditable = false
        newTextView.isSelectable = true
        newTextView.font = NSFont.monospacedSystemFont(ofSize: 11, weight: .regular)
        newTextView.textColor = NSColor.textColor
        newTextView.backgroundColor = NSColor.textBackgroundColor
        newTextView.isRichText = true
        newTextView.allowsUndo = false

        // Store weak reference
        consoleTextView = newTextView

        scrollView.documentView = newTextView
        contentView.addSubview(scrollView)

        // Load existing log content
        newTextView.textStorage?.setAttributedString(consoleLogContent)

        // Scroll to bottom
        newTextView.scrollToEndOfDocument(nil)

        // Show the window
        newWindow.makeKeyAndOrderFront(nil)
    }

    // MARK: - Console Log Management

    public func addToConsoleLog(_ message: String) {
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .medium)
        let logEntry = "[\(timestamp)] \(message)\n"

        DispatchQueue.main.async { [weak self] in
            guard let self = self, !self.isConsoleWindowClosing else { return }

            let attributedLogEntry = NSAttributedString(
                string: logEntry,
                attributes: [
                    .font: NSFont.monospacedSystemFont(ofSize: 11, weight: .regular),
                    .foregroundColor: NSColor.textColor
                ]
            )

            self.consoleLogContent.append(attributedLogEntry)

            // Update text view if it exists and is valid
            if let textView = self.consoleTextView,
               let textStorage = textView.textStorage,
               let window = self.consoleWindow,
               window.isVisible && !self.isConsoleWindowClosing {

                do {
                    textStorage.append(attributedLogEntry)
                    textView.scrollToEndOfDocument(nil)
                } catch {
                    // Silently handle any text storage errors
                    print("Console log update error: \(error)")
                }
            }

            // Limit log size to prevent memory issues (keep last 10000 lines)
            let lines = self.consoleLogContent.string.components(separatedBy: .newlines)
            if lines.count > 10000 {
                let linesToKeep = lines.suffix(10000)
                let newContent = linesToKeep.joined(separator: "\n")
                self.consoleLogContent.mutableString.setString(newContent)

                // Only update text view if it's still valid
                if let textView = self.consoleTextView,
                   let textStorage = textView.textStorage,
                   let window = self.consoleWindow,
                   window.isVisible && !self.isConsoleWindowClosing {

                    do {
                        textStorage.setAttributedString(self.consoleLogContent)
                        textView.scrollToEndOfDocument(nil)
                    } catch {
                        // Silently handle any text storage errors
                        print("Console log trim error: \(error)")
                    }
                }
            }
        }
    }

    // MARK: - Configuration Window Methods

    @objc private func showConfigurationWindow() {
        // Prevent creating window while another is closing
        if isConfigWindowClosing {
            return
        }

        // Always create a new window if the current one is nil or not visible
        if configWindow == nil {
            createConfigurationWindow()
        } else if configWindow?.isVisible == false {
            // Window exists but is not visible, show it
            configWindow?.makeKeyAndOrderFront(nil)
        } else {
            // Window is already visible, just bring it to front
            configWindow?.makeKeyAndOrderFront(nil)
        }

        NSApp.activate(ignoringOtherApps: true)
    }

    private func createConfigurationWindow() {
        // Prevent creating window while another is closing
        if isConfigWindowClosing {
            return
        }

        // Clean up any existing window first
        if let existingWindow = configWindow {
            existingWindow.delegate = nil
            existingWindow.orderOut(nil)
            configWindow = nil
        }

        // Create window
        let windowRect = NSRect(x: 100, y: 100, width: 500, height: 400)
        let newWindow = NSWindow(
            contentRect: windowRect,
            styleMask: [.titled, .closable, .resizable],
            backing: .buffered,
            defer: false
        )

        newWindow.title = "Resource Manager - Configuration"
        newWindow.center()
        newWindow.isReleasedWhenClosed = false
        newWindow.delegate = self
        configWindow = newWindow

        // Create configuration view
        createConfigurationView(in: newWindow)

        // Show the window
        newWindow.makeKeyAndOrderFront(nil)
    }

    private func createConfigurationView(in window: NSWindow) {
        guard let contentView = window.contentView else { return }

        // Create scroll view
        let scrollView = NSScrollView(frame: contentView.bounds)
        scrollView.autoresizingMask = [.width, .height]
        scrollView.hasVerticalScroller = true
        scrollView.hasHorizontalScroller = false
        scrollView.autohidesScrollers = true

        // Create container view
        let containerView = NSView(frame: NSRect(x: 0, y: 0, width: 480, height: 600))

        var yPosition: CGFloat = 550
        let labelWidth: CGFloat = 150
        let fieldWidth: CGFloat = 200
        let rowHeight: CGFloat = 30
        let margin: CGFloat = 20

        // Title
        let titleLabel = NSTextField(labelWithString: "Default Resource Values")
        titleLabel.font = NSFont.boldSystemFont(ofSize: 16)
        titleLabel.frame = NSRect(x: margin, y: yPosition, width: 300, height: 25)
        containerView.addSubview(titleLabel)
        yPosition -= 40

        // Component ID
        let componentIdLabel = NSTextField(labelWithString: "Component ID:")
        componentIdLabel.frame = NSRect(x: margin, y: yPosition, width: labelWidth, height: rowHeight)
        containerView.addSubview(componentIdLabel)

        let componentIdField = NSTextField(frame: NSRect(x: margin + labelWidth + 10, y: yPosition, width: fieldWidth, height: rowHeight))
        componentIdField.stringValue = String(ResourceManagerConfig.shared.componentId)
        componentIdField.tag = 1001
        containerView.addSubview(componentIdField)
        yPosition -= 35

        // DRI ID
        let driIdLabel = NSTextField(labelWithString: "DRI ID:")
        driIdLabel.frame = NSRect(x: margin, y: yPosition, width: labelWidth, height: rowHeight)
        containerView.addSubview(driIdLabel)

        let driIdField = NSTextField(frame: NSRect(x: margin + labelWidth + 10, y: yPosition, width: fieldWidth, height: rowHeight))
        driIdField.stringValue = String(ResourceManagerConfig.shared.driId)
        driIdField.tag = 1002
        containerView.addSubview(driIdField)
        yPosition -= 35

        // Inventory Keeper ID
        let inventoryKeeperIdLabel = NSTextField(labelWithString: "Inventory Keeper ID:")
        inventoryKeeperIdLabel.frame = NSRect(x: margin, y: yPosition, width: labelWidth, height: rowHeight)
        containerView.addSubview(inventoryKeeperIdLabel)

        let inventoryKeeperIdField = NSTextField(frame: NSRect(x: margin + labelWidth + 10, y: yPosition, width: fieldWidth, height: rowHeight))
        inventoryKeeperIdField.stringValue = String(ResourceManagerConfig.shared.inventoryKeeperId)
        inventoryKeeperIdField.tag = 1003
        containerView.addSubview(inventoryKeeperIdField)
        yPosition -= 35

        // Location ID
        let locationIdLabel = NSTextField(labelWithString: "Location ID:")
        locationIdLabel.frame = NSRect(x: margin, y: yPosition, width: labelWidth, height: rowHeight)
        containerView.addSubview(locationIdLabel)

        let locationIdField = NSTextField(frame: NSRect(x: margin + labelWidth + 10, y: yPosition, width: fieldWidth, height: rowHeight))
        locationIdField.stringValue = String(ResourceManagerConfig.shared.locationId)
        locationIdField.tag = 1004
        containerView.addSubview(locationIdField)
        yPosition -= 35

        // Specific Location
        let specificLocationLabel = NSTextField(labelWithString: "Specific Location:")
        specificLocationLabel.frame = NSRect(x: margin, y: yPosition, width: labelWidth, height: rowHeight)
        containerView.addSubview(specificLocationLabel)

        let specificLocationField = NSTextField(frame: NSRect(x: margin + labelWidth + 10, y: yPosition, width: fieldWidth, height: rowHeight))
        specificLocationField.stringValue = ResourceManagerConfig.shared.specificLocation
        specificLocationField.tag = 1005
        containerView.addSubview(specificLocationField)
        yPosition -= 35

        // Class ID
        let classIdLabel = NSTextField(labelWithString: "Class ID:")
        classIdLabel.frame = NSRect(x: margin, y: yPosition, width: labelWidth, height: rowHeight)
        containerView.addSubview(classIdLabel)

        let classIdField = NSTextField(frame: NSRect(x: margin + labelWidth + 10, y: yPosition, width: fieldWidth, height: rowHeight))
        classIdField.stringValue = String(ResourceManagerConfig.shared.classId)
        classIdField.tag = 1006
        containerView.addSubview(classIdField)
        yPosition -= 35

        // State ID
        let stateIdLabel = NSTextField(labelWithString: "State ID:")
        stateIdLabel.frame = NSRect(x: margin, y: yPosition, width: labelWidth, height: rowHeight)
        containerView.addSubview(stateIdLabel)

        let stateIdField = NSTextField(frame: NSRect(x: margin + labelWidth + 10, y: yPosition, width: fieldWidth, height: rowHeight))
        stateIdField.stringValue = String(ResourceManagerConfig.shared.stateId)
        stateIdField.tag = 1007
        containerView.addSubview(stateIdField)
        yPosition -= 50

        // Buttons
        let buttonWidth: CGFloat = 80
        let buttonHeight: CGFloat = 30

        // Save button
        let saveButton = NSButton(frame: NSRect(x: margin + labelWidth + fieldWidth - buttonWidth, y: yPosition, width: buttonWidth, height: buttonHeight))
        saveButton.title = "Save"
        saveButton.bezelStyle = .rounded
        saveButton.target = self
        saveButton.action = #selector(saveConfiguration(_:))
        containerView.addSubview(saveButton)

        // Cancel button
        let cancelButton = NSButton(frame: NSRect(x: margin + labelWidth + fieldWidth - buttonWidth - 90, y: yPosition, width: buttonWidth, height: buttonHeight))
        cancelButton.title = "Cancel"
        cancelButton.bezelStyle = .rounded
        cancelButton.target = self
        cancelButton.action = #selector(cancelConfiguration(_:))
        containerView.addSubview(cancelButton)

        // Reset button
        let resetButton = NSButton(frame: NSRect(x: margin + labelWidth + fieldWidth - buttonWidth - 180, y: yPosition, width: buttonWidth, height: buttonHeight))
        resetButton.title = "Reset"
        resetButton.bezelStyle = .rounded
        resetButton.target = self
        resetButton.action = #selector(resetConfiguration(_:))
        containerView.addSubview(resetButton)

        scrollView.documentView = containerView
        contentView.addSubview(scrollView)
    }

    @objc private func saveConfiguration(_ sender: NSButton) {
        guard let window = configWindow,
              let containerView = window.contentView?.subviews.first?.subviews.first else { return }

        // Extract values from text fields
        for subview in containerView.subviews {
            if let textField = subview as? NSTextField {
                switch textField.tag {
                case 1001: // Component ID
                    if let value = Int(textField.stringValue) {
                        ResourceManagerConfig.shared.componentId = value
                    }
                case 1002: // DRI ID
                    if let value = Int(textField.stringValue) {
                        ResourceManagerConfig.shared.driId = value
                    }
                case 1003: // Inventory Keeper ID
                    if let value = Int(textField.stringValue) {
                        ResourceManagerConfig.shared.inventoryKeeperId = value
                    }
                case 1004: // Location ID
                    if let value = Int(textField.stringValue) {
                        ResourceManagerConfig.shared.locationId = value
                    }
                case 1005: // Specific Location
                    ResourceManagerConfig.shared.specificLocation = textField.stringValue
                case 1006: // Class ID
                    if let value = Int(textField.stringValue) {
                        ResourceManagerConfig.shared.classId = value
                    }
                case 1007: // State ID
                    if let value = Int(textField.stringValue) {
                        ResourceManagerConfig.shared.stateId = value
                    }
                default:
                    break
                }
            }
        }

        // Save configuration
        ResourceManagerConfig.shared.saveConfiguration()

        // Show success message
        let alert = NSAlert()
        alert.messageText = "Configuration Saved"
        alert.informativeText = "Default values have been saved successfully."
        alert.alertStyle = .informational
        alert.addButton(withTitle: "OK")
        alert.runModal()

        // Close window
        window.close()
    }

    @objc private func cancelConfiguration(_ sender: NSButton) {
        configWindow?.close()
    }

    @objc private func resetConfiguration(_ sender: NSButton) {
        // Show confirmation dialog
        let alert = NSAlert()
        alert.messageText = "Reset Configuration"
        alert.informativeText = "Are you sure you want to reset all configuration values to their defaults? This action cannot be undone."
        alert.alertStyle = .warning
        alert.addButton(withTitle: "Reset")
        alert.addButton(withTitle: "Cancel")

        let response = alert.runModal()
        if response == .alertFirstButtonReturn {
            // Reset configuration
            ResourceManagerConfig.shared.resetToDefaults()

            // Update the UI fields
            guard let window = configWindow,
                  let containerView = window.contentView?.subviews.first?.subviews.first else { return }

            for subview in containerView.subviews {
                if let textField = subview as? NSTextField {
                    switch textField.tag {
                    case 1001: // Component ID
                        textField.stringValue = String(ResourceManagerConfig.shared.componentId)
                    case 1002: // DRI ID
                        textField.stringValue = String(ResourceManagerConfig.shared.driId)
                    case 1003: // Inventory Keeper ID
                        textField.stringValue = String(ResourceManagerConfig.shared.inventoryKeeperId)
                    case 1004: // Location ID
                        textField.stringValue = String(ResourceManagerConfig.shared.locationId)
                    case 1005: // Specific Location
                        textField.stringValue = ResourceManagerConfig.shared.specificLocation
                    case 1006: // Class ID
                        textField.stringValue = String(ResourceManagerConfig.shared.classId)
                    case 1007: // State ID
                        textField.stringValue = String(ResourceManagerConfig.shared.stateId)
                    default:
                        break
                    }
                }
            }

            // Show success message
            let successAlert = NSAlert()
            successAlert.messageText = "Configuration Reset"
            successAlert.informativeText = "All configuration values have been reset to their defaults."
            successAlert.alertStyle = .informational
            successAlert.addButton(withTitle: "OK")
            successAlert.runModal()
        }
    }

    // MARK: - NSWindowDelegate

    func windowShouldClose(_ sender: NSWindow) -> Bool {
        if sender == consoleWindow {
            isConsoleWindowClosing = true
            return true
        } else if sender == configWindow {
            isConfigWindowClosing = true
            return true
        }
        return true
    }

    func windowWillClose(_ notification: Notification) {
        if let window = notification.object as? NSWindow {
            if window == consoleWindow {
                // Clear delegate first to prevent further callbacks
                window.delegate = nil
                // Clear references when console window is closed
                consoleWindow = nil
                consoleTextView = nil

                // Reset closing flag after a delay to allow window to fully close
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
                    self?.isConsoleWindowClosing = false
                }
            } else if window == configWindow {
                // Clear delegate first to prevent further callbacks
                window.delegate = nil
                // Clear references when config window is closed
                configWindow = nil

                // Reset closing flag after a delay to allow window to fully close
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
                    self?.isConfigWindowClosing = false
                }
            }
        }
    }
}
